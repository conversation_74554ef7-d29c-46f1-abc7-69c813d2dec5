/*! This file is auto-generated */
(()=>{var i={659:t=>{var e=wp.media.view.l10n,e=wp.media.controller.State.extend({defaults:{id:"edit-attachment",title:e.attachmentDetails,content:"edit-metadata",menu:!1,toolbar:!1,router:!1}});t.exports=e},682:t=>{var e=wp.media.view.Button,i=wp.media.view.l10n,o=e.extend({initialize:function(){_.defaults(this.options,{size:""}),e.prototype.initialize.apply(this,arguments),this.controller.on("select:activate select:deactivate",this.toggleBulkEditHandler,this),this.controller.on("selection:action:done",this.back,this)},back:function(){this.controller.deactivateMode("select").activateMode("edit")},click:function(){e.prototype.click.apply(this,arguments),this.controller.isModeActive("select")?this.back():this.controller.deactivateMode("edit").activateMode("select")},render:function(){return e.prototype.render.apply(this,arguments),this.$el.addClass("select-mode-toggle-button"),this},toggleBulkEditHandler:function(){var t=this.controller.content.get().toolbar,e=t.$(".media-toolbar-secondary > *, .media-toolbar-primary > *");this.controller.isModeActive("select")?(this.model.set({size:"large",text:i.cancel}),e.not(".spinner, .media-button").hide(),this.$el.show(),t.$el.addClass("media-toolbar-mode-select"),t.$(".delete-selected-button").removeClass("hidden")):(this.model.set({size:"",text:i.bulkSelect}),this.controller.content.get().$el.removeClass("fixed"),t.$el.css("width",""),t.$el.removeClass("media-toolbar-mode-select"),t.$(".delete-selected-button").addClass("hidden"),e.not(".media-button").show(),this.controller.state().get("selection").reset())}});t.exports=o},1003:t=>{var e=wp.media.view.Frame,i=wp.media.view.MediaFrame,o=jQuery,i=i.extend({className:"edit-attachment-frame",template:wp.template("edit-attachment-frame"),regions:["title","content"],events:{"click .left":"previousMediaItem","click .right":"nextMediaItem"},initialize:function(){e.prototype.initialize.apply(this,arguments),_.defaults(this.options,{modal:!0,state:"edit-attachment"}),this.controller=this.options.controller,this.gridRouter=this.controller.gridRouter,this.library=this.options.library,this.options.model&&(this.model=this.options.model),this.bindHandlers(),this.createStates(),this.createModal(),this.title.mode("default"),this.toggleNav()},bindHandlers:function(){this.on("title:create:default",this.createTitle,this),this.on("content:create:edit-metadata",this.editMetadataMode,this),this.on("content:create:edit-image",this.editImageMode,this),this.on("content:render:edit-image",this.editImageModeRender,this),this.on("refresh",this.rerender,this),this.on("close",this.detach),this.bindModelHandlers(),this.listenTo(this.gridRouter,"route:search",this.close,this)},bindModelHandlers:function(){this.listenTo(this.model,"change:status destroy",this.close,this)},createModal:function(){this.options.modal&&(this.modal=new wp.media.view.Modal({controller:this,title:this.options.title,hasCloseButton:!1}),this.modal.on("open",_.bind(function(){o("body").on("keydown.media-modal",_.bind(this.keyEvent,this))},this)),this.modal.on("close",_.bind(function(){o("body").off("keydown.media-modal"),o('li.attachment[data-id="'+this.model.get("id")+'"]').trigger("focus"),this.resetRoute()},this)),this.modal.content(this),this.modal.open())},createStates:function(){this.states.add([new wp.media.controller.EditAttachmentMetadata({model:this.model,library:this.library})])},editMetadataMode:function(t){t.view=new wp.media.view.Attachment.Details.TwoColumn({controller:this,model:this.model}),t.view.views.set(".attachment-compat",new wp.media.view.AttachmentCompat({controller:this,model:this.model})),this.model&&!this.model.get("skipHistory")&&this.gridRouter.navigate(this.gridRouter.baseUrl("?item="+this.model.id))},editImageMode:function(t){var e=new wp.media.controller.EditImage({model:this.model,frame:this});e._toolbar=function(){},e._router=function(){},e._menu=function(){},t.view=new wp.media.view.EditImage.Details({model:this.model,frame:this,controller:e}),this.gridRouter.navigate(this.gridRouter.baseUrl("?item="+this.model.id+"&mode=edit"))},editImageModeRender:function(t){t.on("ready",t.loadEditor)},toggleNav:function(){this.$(".left").prop("disabled",!this.hasPrevious()),this.$(".right").prop("disabled",!this.hasNext())},rerender:function(t){this.stopListening(this.model),this.model=t,this.bindModelHandlers(),"edit-metadata"!==this.content.mode()?this.content.mode("edit-metadata"):this.content.render(),this.toggleNav()},previousMediaItem:function(){this.hasPrevious()&&(this.trigger("refresh",this.library.at(this.getCurrentIndex()-1)),this.focusNavButton(this.hasPrevious()?".left":".right"))},nextMediaItem:function(){this.hasNext()&&(this.trigger("refresh",this.library.at(this.getCurrentIndex()+1)),this.focusNavButton(this.hasNext()?".right":".left"))},focusNavButton:function(t){o(t).trigger("focus")},getCurrentIndex:function(){return this.library.indexOf(this.model)},hasNext:function(){return this.getCurrentIndex()+1<this.library.length},hasPrevious:function(){return-1<this.getCurrentIndex()-1},keyEvent:function(t){("INPUT"!==t.target.nodeName&&"TEXTAREA"!==t.target.nodeName||t.target.disabled)&&(39===t.keyCode&&this.nextMediaItem(),37===t.keyCode)&&this.previousMediaItem()},resetRoute:function(){var t=this.controller.browserView.toolbar.get("search").$el.val();this.gridRouter.navigate(this.gridRouter.baseUrl(""!==t?"?search="+t:""),{replace:!0})}});t.exports=i},1312:t=>{var e=wp.media.view.Attachment.Details,i=e.extend({template:wp.template("attachment-details-two-column"),initialize:function(){this.controller.on("content:activate:edit-details",_.bind(this.editAttachment,this)),e.prototype.initialize.apply(this,arguments)},editAttachment:function(t){t&&t.preventDefault(),this.controller.content.mode("edit-image")},toggleSelectionHandler:function(){}});t.exports=i},2429:t=>{var e=Backbone.Router.extend({routes:{"upload.php?item=:slug&mode=edit":"editItem","upload.php?item=:slug":"showItem","upload.php?search=:query":"search","upload.php":"reset"},baseUrl:function(t){return"upload.php"+t},reset:function(){var t=wp.media.frames.edit;t&&t.close()},search:function(t){jQuery("#media-search-input").val(t).trigger("input")},showItem:function(t){var e=wp.media,i=e.frames.browse,o=i.state().get("library").findWhere({id:parseInt(t,10)});o?(o.set("skipHistory",!0),i.trigger("edit:attachment",o)):(o=e.attachment(t),i.listenTo(o,"change",function(t){i.stopListening(o),i.trigger("edit:attachment",t)}),o.fetch())},editItem:function(t){this.showItem(t),wp.media.frames.edit.content.mode("edit-details")}});t.exports=e},5806:t=>{var e=wp.media.view.Button,i=wp.media.view.DeleteSelectedButton,o=i.extend({initialize:function(){i.prototype.initialize.apply(this,arguments),this.controller.on("select:activate",this.selectActivate,this),this.controller.on("select:deactivate",this.selectDeactivate,this)},filterChange:function(t){this.canShow="trash"===t.get("status")},selectActivate:function(){this.toggleDisabled(),this.$el.toggleClass("hidden",!this.canShow)},selectDeactivate:function(){this.toggleDisabled(),this.$el.addClass("hidden")},render:function(){return e.prototype.render.apply(this,arguments),this.selectActivate(),this}});t.exports=o},6606:t=>{var e=wp.media.view.Button,i=wp.media.view.l10n,o=e.extend({initialize:function(){e.prototype.initialize.apply(this,arguments),this.options.filters&&this.options.filters.model.on("change",this.filterChange,this),this.controller.on("selection:toggle",this.toggleDisabled,this),this.controller.on("select:activate",this.toggleDisabled,this)},filterChange:function(t){"trash"===t.get("status")?this.model.set("text",i.restoreSelected):wp.media.view.settings.mediaTrash?this.model.set("text",i.trashSelected):this.model.set("text",i.deletePermanently)},toggleDisabled:function(){this.model.set("disabled",!this.controller.state().get("selection").length)},render:function(){return e.prototype.render.apply(this,arguments),this.controller.isModeActive("select")?this.$el.addClass("delete-selected-button"):this.$el.addClass("delete-selected-button hidden"),this.toggleDisabled(),this}});t.exports=o},8359:t=>{var e=wp.media.view.MediaFrame,i=wp.media.controller.Library,s=Backbone.$,o=e.extend({initialize:function(){_.defaults(this.options,{title:"",modal:!1,selection:[],library:{},multiple:"add",state:"library",uploader:!0,mode:["grid","edit"]}),this.$body=s(document.body),this.$window=s(window),this.$adminBar=s("#wpadminbar"),this.$uploaderToggler=s(".page-title-action").attr("aria-expanded","false").on("click",_.bind(this.addNewClickHandler,this)),this.$window.on("scroll resize",_.debounce(_.bind(this.fixPosition,this),15)),this.$el.addClass("wp-core-ui"),!wp.Uploader.limitExceeded&&wp.Uploader.browser.supported||(this.options.uploader=!1),this.options.uploader&&(this.uploader=new wp.media.view.UploaderWindow({controller:this,uploader:{dropzone:document.body,container:document.body}}).render(),this.uploader.ready(),s("body").append(this.uploader.el),this.options.uploader=!1),this.gridRouter=new wp.media.view.MediaFrame.Manage.Router,e.prototype.initialize.apply(this,arguments),this.$el.appendTo(this.options.container),this.createStates(),this.bindRegionModeHandlers(),this.render(),this.bindSearchHandler(),wp.media.frames.browse=this},bindSearchHandler:function(){var t=this.$("#media-search-input"),e=this.browserView.toolbar.get("search").$el,i=this.$(".view-list"),o=_.throttle(function(t){var t=s(t.currentTarget).val(),e="";t&&this.gridRouter.navigate(this.gridRouter.baseUrl(e+="?search="+t),{replace:!0})},1e3);t.on("input",_.bind(o,this)),this.gridRouter.on("route:search",function(){var t=window.location.href;-1<t.indexOf("mode=")?t=t.replace(/mode=[^&]+/g,"mode=list"):t+=-1<t.indexOf("?")?"&mode=list":"?mode=list",t=t.replace("search=","s="),i.prop("href",t)}).on("route:reset",function(){e.val("").trigger("input")})},createStates:function(){var t=this.options;this.options.states||this.states.add([new i({library:wp.media.query(t.library),multiple:t.multiple,title:t.title,content:"browse",toolbar:"select",contentUserSetting:!1,filterable:"all",autoSelect:!1})])},bindRegionModeHandlers:function(){this.on("content:create:browse",this.browseContent,this),this.on("edit:attachment",this.openEditAttachmentModal,this),this.on("select:activate",this.bindKeydown,this),this.on("select:deactivate",this.unbindKeydown,this)},handleKeydown:function(t){27===t.which&&(t.preventDefault(),this.deactivateMode("select").activateMode("edit"))},bindKeydown:function(){this.$body.on("keydown.select",_.bind(this.handleKeydown,this))},unbindKeydown:function(){this.$body.off("keydown.select")},fixPosition:function(){var t,e;this.isModeActive("select")&&(e=(t=this.$(".attachments-browser")).find(".media-toolbar"),t.offset().top+16<this.$window.scrollTop()+this.$adminBar.height()?(t.addClass("fixed"),e.css("width",t.width()+"px")):(t.removeClass("fixed"),e.css("width","")))},addNewClickHandler:function(t){t.preventDefault(),this.trigger("toggle:upload:attachment"),this.uploader&&this.uploader.refresh()},openEditAttachmentModal:function(t){wp.media.frames.edit?wp.media.frames.edit.open().trigger("refresh",t):wp.media.frames.edit=wp.media({frame:"edit-attachments",controller:this,library:this.state().get("library"),model:t})},browseContent:function(t){var e=this.state();this.browserView=t.view=new wp.media.view.AttachmentsBrowser({controller:this,collection:e.get("library"),selection:e.get("selection"),model:e,sortable:e.get("sortable"),search:e.get("searchable"),filters:e.get("filterable"),date:e.get("date"),display:e.get("displaySettings"),dragInfo:e.get("dragInfo"),sidebar:"errors",suggestedWidth:e.get("suggestedWidth"),suggestedHeight:e.get("suggestedHeight"),AttachmentView:e.get("AttachmentView"),scrollElement:document}),this.browserView.on("ready",_.bind(this.bindDeferred,this)),this.errors=wp.Uploader.errors,this.errors.on("add remove reset",this.sidebarVisibility,this)},sidebarVisibility:function(){this.browserView.$(".media-sidebar").toggle(!!this.errors.length)},bindDeferred:function(){this.browserView.dfd&&this.browserView.dfd.done(_.bind(this.startHistory,this))},startHistory:function(){window.history&&window.history.pushState&&(Backbone.History.started&&Backbone.history.stop(),Backbone.history.start({root:window._wpMediaGridSettings.adminUrl,pushState:!0}))}});t.exports=o},8521:t=>{var e=wp.media.View,i=wp.media.view.EditImage.extend({initialize:function(t){this.editor=window.imageEdit,this.frame=t.frame,this.controller=t.controller,e.prototype.initialize.apply(this,arguments)},back:function(){this.frame.content.mode("edit-metadata")},save:function(){this.model.fetch().done(_.bind(function(){this.frame.content.mode("edit-metadata")},this))}});t.exports=i}},o={};function s(t){var e=o[t];return void 0!==e||(e=o[t]={exports:{}},i[t](e,e.exports,s)),e.exports}var t=wp.media;t.controller.EditAttachmentMetadata=s(659),t.view.MediaFrame.Manage=s(8359),t.view.Attachment.Details.TwoColumn=s(1312),t.view.MediaFrame.Manage.Router=s(2429),t.view.EditImage.Details=s(8521),t.view.MediaFrame.EditAttachments=s(1003),t.view.SelectModeToggleButton=s(682),t.view.DeleteSelectedButton=s(6606),t.view.DeleteSelectedPermanentlyButton=s(5806)})();