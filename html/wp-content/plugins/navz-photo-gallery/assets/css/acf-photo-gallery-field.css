.acf-photo-gallery-metabox-list li {
    display: inline-table;
    padding: 3px;
    border: 1px solid #ccc;
    margin: 8px;
    position: relative;
    vertical-align: top;
    width: 202px;
    height: 202px;
}

.acf-photo-gallery-metabox-list li img {
    width: 100%;
}

.acf-photo-gallery-metabox-list .acf-photo-gallery-media-box-placeholder {
    width: 150px;
    height: 150px;
    border: 1px dashed #ccc;
}

.acf-photo-gallery-metabox-list .acf-photo-gallery-media-box-placeholder .dashicons-format-image {
    font-size: 150px;
    color: #ccc;
}

.acf-photo-gallery-metabox-list li .dashicons-dismiss {
    position: absolute;
    top: -9px;
    right: -9px;
    color: #fff;
    background: #000000;
    border-radius: 50%;
}

.acf-photo-gallery-metabox-list li .dashicons-edit {
    position: absolute;
    top: -9px;
    right: 12px;
    color: #fff;
    background: #000000;
    border-radius: 50%;
}

.acf-edit-photo-gallery h3{
    background: #f1f1f1;
    padding: 12px 15px;
    border-bottom: 1px solid #ccc;
}

.acf-edit-photo-gallery .save-changes-wrap {
    margin-top: 15px;
}

.acf-edit-photo-gallery label {
    margin-top: 10px;
    display: block;
}

.acf-edit-photo-gallery input:not([type='checkbox']):not([type='radio']) {
    width: 100%;
}

.acf-edit-photo-gallery textarea {
    width: 100%;
}

.acf-photo-gallery-metabox-list li.acf-photo-gallery-sortable-placeholder {
    border: 1px dashed #ccc;
    width: 150px;
    height: 150px;
    display: inline-block;
}

.acf-gallery-backdrop{
    background: #000000;
    opacity: 0.5;
    position: fixed;
    z-index: 9999;
    width: 100%;
    height: 100%;
    top: 0%;
    bottom: 0%;
    left: 0%;
    right: 0%;
}

.acf_pgf_modal {
    display: none;
    position: fixed;
    z-index: 99999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.8);
    -webkit-animation-name: fadeIn;
    -webkit-animation-duration: 0.4s;
            animation-name: fadeIn;
            animation-duration: 0.4s;
}
  
.acf_pgf_modal .acf_pgf_modal-content {
    position: absolute;
    background-color: #fefefe;
    -webkit-animation-name: slideIn;
    -webkit-animation-duration: 0.4s;
            animation-name: slideIn;
            animation-duration: 0.4s;
    width: 30rem;
    left: 50%;
    top: 15%;
    transform: translate(-50%, 0%);
}

.acf_pgf_modal .acf_pgf_modal-header h2 {
    padding: 12px 15px !important;
    margin: 0px !important;
    border-bottom: 1px solid #ccc;
    color: #1d2327;
    font-weight: bold !important;
    font-size: 1.3em !important;
    background: #f1f1f1;
}
  
.acf_pgf_modal .acf_pgf_modal-body {
    padding: 12px;
}

.acf_pgf_modal .acf_pgf_modal-body label{
    width: 100%;
    display: inline-block;
}

.acf_pgf_modal .acf_pgf_modal-body select{
    width: 100%;
}

.acf_pgf_modal.acf_pgf_modal-body p {
    margin-top: 0 !important
}
  
.acf_pgf_modal .acf_pgf_modal-footer {
    text-align: right;
    padding: 12px;
}

.acf_pgf_modal .acf_pgf_modal-footer button {
    margin-left: 5px !important;
}