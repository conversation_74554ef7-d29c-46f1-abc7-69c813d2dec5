/*! elementor-pro - v3.29.0 - 19-05-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[1],{197:(t,e,n)=>{var s=n(6784);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=s(n(8562)),o=s(n(8243));class ContactButtonsHandler extends i.default{clicks=[];getDefaultSettings(){return{selectors:{main:".e-contact-buttons",content:".e-contact-buttons__content",contentWrapper:".e-contact-buttons__content-wrapper",chatButton:".e-contact-buttons__chat-button",closeButton:".e-contact-buttons__close-button",messageBubbleTime:".e-contact-buttons__message-bubble-time"},constants:{entranceAnimation:"style_chat_box_entrance_animation",exitAnimation:"style_chat_box_exit_animation",chatButtonAnimation:"style_chat_button_animation",animated:"animated",animatedWrapper:"animated-wrapper",visible:"visible",reverse:"reverse",hidden:"hidden",hasAnimations:"has-animations",hasEntranceAnimation:"has-entrance-animation",none:"none"}}}getDefaultElements(){const t=this.getSettings("selectors");return{main:this.$element[0].querySelector(t.main),content:this.$element[0].querySelector(t.content),contentWrapper:this.$element[0].querySelector(t.contentWrapper),chatButton:this.$element[0].querySelector(t.chatButton),closeButton:this.$element[0].querySelector(t.closeButton),messageBubbleTime:this.$element[0].querySelector(t.messageBubbleTime)}}getResponsiveSetting(t){const e=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),t,"",e)}bindEvents(){this.elements.closeButton&&this.elements.closeButton.addEventListener("click",this.closeChatBox.bind(this)),this.elements.chatButton&&(this.elements.chatButton.addEventListener("click",this.onChatButtonClick.bind(this)),this.elements.chatButton.addEventListener("animationend",this.removeChatButtonAnimationClasses.bind(this))),this.elements.content&&this.elements.content.addEventListener("animationend",this.removeAnimationClasses.bind(this)),this.elements.contentWrapper&&window.addEventListener("keyup",this.onDocumentKeyup.bind(this))}contentWrapperIsHidden(t){if(!this.elements.contentWrapper)return!1;const{hidden:e}=this.getSettings("constants");return!0===t?(this.elements.contentWrapper.classList.add(e),void this.elements.contentWrapper.setAttribute("aria-hidden","true")):!1===t?(this.elements.contentWrapper.classList.remove(e),void this.elements.contentWrapper.setAttribute("aria-hidden","false")):this.elements.contentWrapper.classList.contains(e)}onDocumentKeyup(t){27===t.keyCode&&this.elements.main&&!this.contentWrapperIsHidden()&&this.elements.main.contains(document.activeElement)&&this.closeChatBox()}onChatButtonTrackClick(t){const e=t.target||t.srcElement,n=this.getSettings("selectors"),s=[n.contactButtonsVar4,n.contactButtonsVar6,n.contactButtonCore];for(const t of s)(e.matches(t)||e.closest(t))&&this.getDocumentIdAndTrack(e,n);(e.matches(n.contactButtonsVar5)||e.closest(n.contactButtonsVar5))&&e.closest(".e-contact-buttons-var-5")&&this.getDocumentIdAndTrack(e,n)}getDocumentIdAndTrack(t,e){let n=t.closest(e.main).dataset.documentId;n||(n=t.closest(e.elementorWrapper).dataset.elementorId),this.trackClick(n)}trackClick(t){t&&(this.clicks.push(t),this.clicks.length>=10&&this.sendClicks())}sendClicks(){const t=new FormData;t.append("action","elementor_send_clicks"),t.append("_nonce",elementorFrontendConfig?.nonces?.floatingButtonsClickTracking),this.clicks.forEach((e=>t.append("clicks[]",e))),fetch(elementorFrontendConfig?.urls?.ajaxurl,{method:"POST",body:t}).then((()=>{this.clicks=[]}))}removeAnimationClasses(){if(!this.elements.content)return;const{reverse:t,entranceAnimation:e,exitAnimation:n,animated:s,visible:i}=this.getSettings("constants"),o=this.elements.content.classList.contains(t),a=this.getResponsiveSetting(e),c=this.getResponsiveSetting(n);o?(this.elements.content.classList.remove(s),this.elements.content.classList.remove(t),c&&this.elements.content.classList.remove(c),this.elements.content.classList.remove(i)):(this.elements.content.classList.remove(s),a&&this.elements.content.classList.remove(a),this.elements.content.classList.add(i))}chatBoxEntranceAnimation(){const{entranceAnimation:t,animated:e,animatedWrapper:n,none:s}=this.getSettings("constants"),i=this.getResponsiveSetting(t);i&&s!==i&&(this.elements.content&&(this.elements.content.classList.add(e),this.elements.content.classList.add(i)),this.elements.contentWrapper&&this.elements.contentWrapper.classList.remove(n))}chatBoxExitAnimation(){const{reverse:t,exitAnimation:e,animated:n,animatedWrapper:s,none:i}=this.getSettings("constants"),o=this.getResponsiveSetting(e);o&&i!==o&&(this.elements.content&&(this.elements.content.classList.add(n),this.elements.content.classList.add(t),this.elements.content.classList.add(o)),this.elements.contentWrapper&&this.elements.contentWrapper.classList.add(s))}openChatBox(){const{hasAnimations:t,visible:e}=this.getSettings("constants");this.elements.main&&this.elements.main.classList.contains(t)?this.chatBoxEntranceAnimation():this.elements.content&&this.elements.content.classList.add(e),this.elements.contentWrapper&&(this.contentWrapperIsHidden(!1),elementorFrontend.isEditMode()||(this.elements.contentWrapper.setAttribute("tabindex","0"),this.elements.contentWrapper.focus({focusVisible:!0}))),this.elements.chatButton&&this.elements.chatButton.setAttribute("aria-expanded","true"),this.elements.closeButton&&this.elements.closeButton.setAttribute("aria-expanded","true")}closeChatBox(){const{hasAnimations:t,visible:e}=this.getSettings("constants");this.elements.main&&this.elements.main.classList.contains(t)?this.chatBoxExitAnimation():this.elements.content&&this.elements.content.classList.remove(e),this.elements.contentWrapper&&this.contentWrapperIsHidden(!0),this.elements.chatButton&&(this.elements.chatButton.setAttribute("aria-expanded","false"),this.elements.chatButton.focus({focusVisible:!0})),this.elements.closeButton&&this.elements.closeButton.setAttribute("aria-expanded","false")}onChatButtonClick(){this.elements.contentWrapper&&this.contentWrapperIsHidden()?this.openChatBox():this.closeChatBox()}initMessageBubbleTime(){if(!this.elements.messageBubbleTime)return;const t="12h"===this.elements.messageBubbleTime.dataset.timeFormat,e=new Intl.DateTimeFormat("default",{hour12:t,hour:"numeric",minute:"numeric"}).format(new Date);this.elements.messageBubbleTime.innerHTML=e}removeChatButtonAnimationClasses(){if(!this.elements.chatButton)return;const{chatButtonAnimation:t,visible:e}=this.getSettings("constants");this.elements.chatButton.classList.remove(t),this.elements.chatButton.classList.add(e)}initChatButtonEntranceAnimation(){const{none:t,chatButtonAnimation:e}=this.getSettings("constants"),n=this.getResponsiveSetting(e);n&&t!==n&&this.elements.chatButton.classList.add(n)}initDefaultState(){if(this.elements.contentWrapper){const t=this.contentWrapperIsHidden();this.elements.chatButton&&this.elements.chatButton.setAttribute("aria-expanded",!t),this.elements.closeButton&&this.elements.closeButton.setAttribute("aria-expanded",!t)}elementorFrontend.isEditMode()&&"floating-buttons"===elementor?.config?.document?.type&&this.openChatBox()}setupInnerContainer(){this.elements.main.closest(".e-con-inner").classList.add("e-con-inner--floating-buttons")}onInit(){const{hasEntranceAnimation:t}=this.getSettings("constants");super.onInit(...arguments),this.clickTrackingHandler=new o.default({$element:this.$element}),this.elements.messageBubbleTime&&this.initMessageBubbleTime(),this.initDefaultState(),this.elements.chatButton&&this.elements.chatButton.classList.contains(t)&&this.initChatButtonEntranceAnimation(),this.setupInnerContainer()}}e.default=ContactButtonsHandler},8243:(t,e,n)=>{var s=n(6784);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=s(n(8562));class ClickTrackingHandler extends i.default{clicks=[];getDefaultSettings(){return{selectors:{contentWrapper:".e-contact-buttons__content-wrapper",contentWrapperFloatingBars:".e-floating-bars",floatingBarCouponButton:".e-floating-bars__coupon-button",floatingBarsHeadline:".e-floating-bars__headline",contactButtonsVar4:".e-contact-buttons__contact-icon-link",contactButtonsVar5:".e-contact-buttons__chat-button",contactButtonsVar6:".e-contact-buttons-var-6",contactButtonsVar8:".e-contact-buttons-var-8",elementorWrapper:'[data-elementor-type="floating-buttons"]',contactButtonCore:".e-contact-buttons__send-button"}}}getDefaultElements(){const t=this.getSettings("selectors");return{contentWrapper:this.$element[0].querySelector(t.contentWrapper),contentWrapperFloatingBars:this.$element[0].querySelector(t.contentWrapperFloatingBars),contactButtonsVar5:this.$element[0].querySelector(t.contactButtonsVar5),contactButtonsVar6:this.$element[0].querySelector(t.contactButtonsVar6)}}bindEvents(){this.elements.contentWrapper&&this.elements.contentWrapper.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contactButtonsVar5&&this.elements.contactButtonsVar5.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contactButtonsVar6&&this.elements.contactButtonsVar6.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contentWrapperFloatingBars&&this.elements.contentWrapperFloatingBars.addEventListener("click",this.onChatButtonTrackClick.bind(this)),window.addEventListener("beforeunload",(()=>{this.clicks.length>0&&this.sendClicks()}))}onChatButtonTrackClick(t){const e=t.target||t.srcElement,n=this.getSettings("selectors"),s=[n.contactButtonsVar4,n.contactButtonsVar6,n.floatingBarCouponButton,n.floatingBarsHeadline,n.contactButtonCore];for(const t of s)(e.matches(t)||e.closest(t))&&this.getDocumentIdAndTrack(e,n);(e.matches(n.contactButtonsVar5)||e.closest(n.contactButtonsVar5))&&e.closest(".e-contact-buttons-var-5")&&this.getDocumentIdAndTrack(e,n)}getDocumentIdAndTrack(t,e){const n=t.closest(e.elementorWrapper).dataset.elementorId;this.trackClick(n)}trackClick(t){t&&(this.clicks.push(t),this.clicks.length>=10&&this.sendClicks())}sendClicks(){const t=new FormData;t.append("action","elementor_send_clicks"),t.append("_nonce",elementorFrontendConfig?.nonces?.floatingButtonsClickTracking),this.clicks.forEach((e=>t.append("clicks[]",e))),fetch(elementorFrontendConfig?.urls?.ajaxurl,{method:"POST",body:t}).then((()=>{this.clicks=[]}))}}e.default=ClickTrackingHandler},8562:t=>{t.exports=elementorModules.ViewModule.extend({$element:null,editorListeners:null,onElementChange:null,onEditSettingsChange:null,onPageSettingsChange:null,isEdit:null,__construct(t){this.isActive(t)&&(this.$element=t.$element,this.isEdit=this.$element.hasClass("elementor-element-edit-mode"),this.isEdit&&this.addEditorListeners())},isActive:()=>!0,isElementInTheCurrentDocument(){return!!elementorFrontend.isEditMode()&&elementor.documents.currentDocument.id.toString()===this.$element[0].closest(".elementor").dataset.elementorId},findElement(t){var e=this.$element;return e.find(t).filter((function(){return jQuery(this).parent().closest(".elementor-element").is(e)}))},getUniqueHandlerID(t,e){return t||(t=this.getModelCID()),e||(e=this.$element),t+e.attr("data-element_type")+this.getConstructorID()},initEditorListeners(){var t=this;if(t.editorListeners=[{event:"element:destroy",to:elementor.channels.data,callback(e){e.cid===t.getModelCID()&&t.onDestroy()}}],t.onElementChange){const e=t.getWidgetType()||t.getElementType();let n="change";"global"!==e&&(n+=":"+e),t.editorListeners.push({event:n,to:elementor.channels.editor,callback(e,n){t.getUniqueHandlerID(n.model.cid,n.$el)===t.getUniqueHandlerID()&&t.onElementChange(e.model.get("name"),e,n)}})}t.onEditSettingsChange&&t.editorListeners.push({event:"change:editSettings",to:elementor.channels.editor,callback(e,n){if(n.model.cid!==t.getModelCID())return;const s=Object.keys(e.changed)[0];t.onEditSettingsChange(s,e.changed[s])}}),["page"].forEach((function(e){var n="on"+e[0].toUpperCase()+e.slice(1)+"SettingsChange";t[n]&&t.editorListeners.push({event:"change",to:elementor.settings[e].model,callback(e){t[n](e.changed)}})}))},getEditorListeners(){return this.editorListeners||this.initEditorListeners(),this.editorListeners},addEditorListeners(){var t=this.getUniqueHandlerID();this.getEditorListeners().forEach((function(e){elementorFrontend.addListenerOnce(t,e.event,e.callback,e.to)}))},removeEditorListeners(){var t=this.getUniqueHandlerID();this.getEditorListeners().forEach((function(e){elementorFrontend.removeListeners(t,e.event,null,e.to)}))},getElementType(){return this.$element.data("element_type")},getWidgetType(){const t=this.$element.data("widget_type");if(t)return t.split(".")[0]},getID(){return this.$element.data("id")},getModelCID(){return this.$element.data("model-cid")},getElementSettings(t){let e={};const n=this.getModelCID();if(this.isEdit&&n){const t=elementorFrontend.config.elements.data[n],s=t.attributes;let i=s.widgetType||s.elType;s.isInner&&(i="inner-"+i);let o=elementorFrontend.config.elements.keys[i];o||(o=elementorFrontend.config.elements.keys[i]=[],jQuery.each(t.controls,((t,e)=>{(e.frontend_available||e.editor_available)&&o.push(t)}))),jQuery.each(t.getActiveControls(),(function(t){if(-1!==o.indexOf(t)){let n=s[t];n.toJSON&&(n=n.toJSON()),e[t]=n}}))}else e=this.$element.data("settings")||{};return this.getItems(e,t)},getEditSettings(t){var e={};return this.isEdit&&(e=elementorFrontend.config.elements.editSettings[this.getModelCID()].attributes),this.getItems(e,t)},getCurrentDeviceSetting(t){return elementorFrontend.getCurrentDeviceSetting(this.getElementSettings(),t)},onInit(){this.isActive(this.getSettings())&&elementorModules.ViewModule.prototype.onInit.apply(this,arguments)},onDestroy(){this.isEdit&&this.removeEditorListeners(),this.unbindEvents&&this.unbindEvents()}})}}]);