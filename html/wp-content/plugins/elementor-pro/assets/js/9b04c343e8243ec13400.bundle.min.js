/*! elementor-pro - v3.29.0 - 19-05-2025 */
/*! For license information please see 9b04c343e8243ec13400.bundle.min.js.LICENSE.txt */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[786],{4786:e=>{const{entries:t,setPrototypeOf:n,isFrozen:o,getPrototypeOf:a,getOwnPropertyDescriptor:r}=Object;let{freeze:i,seal:l,create:c}=Object,{apply:s,construct:u}="undefined"!=typeof Reflect&&Reflect;i||(i=function freeze(e){return e}),l||(l=function seal(e){return e}),s||(s=function apply(e,t,n){return e.apply(t,n)}),u||(u=function construct(e,t){return new e(...t)});const p=unapply(Array.prototype.forEach),d=unapply(Array.prototype.lastIndexOf),m=unapply(Array.prototype.pop),f=unapply(Array.prototype.push),h=unapply(Array.prototype.splice),T=unapply(String.prototype.toLowerCase),g=unapply(String.prototype.toString),y=unapply(String.prototype.match),S=unapply(String.prototype.replace),_=unapply(String.prototype.indexOf),E=unapply(String.prototype.trim),A=unapply(Object.prototype.hasOwnProperty),b=unapply(RegExp.prototype.test),N=function unconstruct(e){return function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return u(e,n)}}(TypeError);function unapply(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];return s(e,t,o)}}function addToSet(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:T;n&&n(e,null);let r=t.length;for(;r--;){let n=t[r];if("string"==typeof n){const e=a(n);e!==n&&(o(t)||(t[r]=e),n=e)}e[n]=!0}return e}function cleanArray(e){for(let t=0;t<e.length;t++){A(e,t)||(e[t]=null)}return e}function clone(e){const n=c(null);for(const[o,a]of t(e)){A(e,o)&&(Array.isArray(a)?n[o]=cleanArray(a):a&&"object"==typeof a&&a.constructor===Object?n[o]=clone(a):n[o]=a)}return n}function lookupGetter(e,t){for(;null!==e;){const n=r(e,t);if(n){if(n.get)return unapply(n.get);if("function"==typeof n.value)return unapply(n.value)}e=a(e)}return function fallbackValue(){return null}}const w=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),R=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),k=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),O=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),x=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),D=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),C=i(["#text"]),v=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),L=i(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),M=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),I=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),z=l(/\{\{[\w\W]*|[\w\W]*\}\}/gm),P=l(/<%[\w\W]*|[\w\W]*%>/gm),U=l(/\$\{[\w\W]*/gm),H=l(/^data-[\-\w.\u00B7-\uFFFF]+$/),F=l(/^aria-[\-\w]+$/),G=l(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),B=l(/^(?:\w+script|data):/i),W=l(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Y=l(/^html$/i),j=l(/^[a-z][.\w]*(-[.\w]+)+$/i);var X=Object.freeze({__proto__:null,ARIA_ATTR:F,ATTR_WHITESPACE:W,CUSTOM_ELEMENT:j,DATA_ATTR:H,DOCTYPE_NAME:Y,ERB_EXPR:P,IS_ALLOWED_URI:G,IS_SCRIPT_OR_DATA:B,MUSTACHE_EXPR:z,TMPLIT_EXPR:U});const q=1,V=3,$=7,K=8,Z=9,J=function getGlobal(){return"undefined"==typeof window?null:window};var Q=function createDOMPurify(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:J();const DOMPurify=e=>createDOMPurify(e);if(DOMPurify.version="3.2.5",DOMPurify.removed=[],!e||!e.document||e.document.nodeType!==Z||!e.Element)return DOMPurify.isSupported=!1,DOMPurify;let{document:n}=e;const o=n,a=o.currentScript,{DocumentFragment:r,HTMLTemplateElement:l,Node:s,Element:u,NodeFilter:z,NamedNodeMap:P=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:U,DOMParser:H,trustedTypes:F}=e,B=u.prototype,W=lookupGetter(B,"cloneNode"),j=lookupGetter(B,"remove"),Q=lookupGetter(B,"nextSibling"),ee=lookupGetter(B,"childNodes"),te=lookupGetter(B,"parentNode");if("function"==typeof l){const e=n.createElement("template");e.content&&e.content.ownerDocument&&(n=e.content.ownerDocument)}let ne,oe="";const{implementation:ae,createNodeIterator:re,createDocumentFragment:ie,getElementsByTagName:le}=n,{importNode:ce}=o;let se={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};DOMPurify.isSupported="function"==typeof t&&"function"==typeof te&&ae&&void 0!==ae.createHTMLDocument;const{MUSTACHE_EXPR:ue,ERB_EXPR:pe,TMPLIT_EXPR:de,DATA_ATTR:me,ARIA_ATTR:fe,IS_SCRIPT_OR_DATA:he,ATTR_WHITESPACE:Te,CUSTOM_ELEMENT:ge}=X;let{IS_ALLOWED_URI:ye}=X,Se=null;const _e=addToSet({},[...w,...R,...k,...x,...C]);let Ee=null;const Ae=addToSet({},[...v,...L,...M,...I]);let be=Object.seal(c(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ne=null,we=null,Re=!0,ke=!0,Oe=!1,xe=!0,De=!1,Ce=!0,ve=!1,Le=!1,Me=!1,Ie=!1,ze=!1,Pe=!1,Ue=!0,He=!1,Fe=!0,Ge=!1,Be={},We=null;const Ye=addToSet({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let je=null;const Xe=addToSet({},["audio","video","img","source","image","track"]);let qe=null;const Ve=addToSet({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),$e="http://www.w3.org/1998/Math/MathML",Ke="http://www.w3.org/2000/svg",Ze="http://www.w3.org/1999/xhtml";let Je=Ze,Qe=!1,et=null;const tt=addToSet({},[$e,Ke,Ze],g);let nt=addToSet({},["mi","mo","mn","ms","mtext"]),ot=addToSet({},["annotation-xml"]);const at=addToSet({},["title","style","font","a","script"]);let rt=null;const it=["application/xhtml+xml","text/html"];let lt=null,ct=null;const st=n.createElement("form"),ut=function isRegexOrFunction(e){return e instanceof RegExp||e instanceof Function},pt=function _parseConfig(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!ct||ct!==e){if(e&&"object"==typeof e||(e={}),e=clone(e),rt=-1===it.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,lt="application/xhtml+xml"===rt?g:T,Se=A(e,"ALLOWED_TAGS")?addToSet({},e.ALLOWED_TAGS,lt):_e,Ee=A(e,"ALLOWED_ATTR")?addToSet({},e.ALLOWED_ATTR,lt):Ae,et=A(e,"ALLOWED_NAMESPACES")?addToSet({},e.ALLOWED_NAMESPACES,g):tt,qe=A(e,"ADD_URI_SAFE_ATTR")?addToSet(clone(Ve),e.ADD_URI_SAFE_ATTR,lt):Ve,je=A(e,"ADD_DATA_URI_TAGS")?addToSet(clone(Xe),e.ADD_DATA_URI_TAGS,lt):Xe,We=A(e,"FORBID_CONTENTS")?addToSet({},e.FORBID_CONTENTS,lt):Ye,Ne=A(e,"FORBID_TAGS")?addToSet({},e.FORBID_TAGS,lt):{},we=A(e,"FORBID_ATTR")?addToSet({},e.FORBID_ATTR,lt):{},Be=!!A(e,"USE_PROFILES")&&e.USE_PROFILES,Re=!1!==e.ALLOW_ARIA_ATTR,ke=!1!==e.ALLOW_DATA_ATTR,Oe=e.ALLOW_UNKNOWN_PROTOCOLS||!1,xe=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,De=e.SAFE_FOR_TEMPLATES||!1,Ce=!1!==e.SAFE_FOR_XML,ve=e.WHOLE_DOCUMENT||!1,Ie=e.RETURN_DOM||!1,ze=e.RETURN_DOM_FRAGMENT||!1,Pe=e.RETURN_TRUSTED_TYPE||!1,Me=e.FORCE_BODY||!1,Ue=!1!==e.SANITIZE_DOM,He=e.SANITIZE_NAMED_PROPS||!1,Fe=!1!==e.KEEP_CONTENT,Ge=e.IN_PLACE||!1,ye=e.ALLOWED_URI_REGEXP||G,Je=e.NAMESPACE||Ze,nt=e.MATHML_TEXT_INTEGRATION_POINTS||nt,ot=e.HTML_INTEGRATION_POINTS||ot,be=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ut(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(be.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ut(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(be.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(be.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),De&&(ke=!1),ze&&(Ie=!0),Be&&(Se=addToSet({},C),Ee=[],!0===Be.html&&(addToSet(Se,w),addToSet(Ee,v)),!0===Be.svg&&(addToSet(Se,R),addToSet(Ee,L),addToSet(Ee,I)),!0===Be.svgFilters&&(addToSet(Se,k),addToSet(Ee,L),addToSet(Ee,I)),!0===Be.mathMl&&(addToSet(Se,x),addToSet(Ee,M),addToSet(Ee,I))),e.ADD_TAGS&&(Se===_e&&(Se=clone(Se)),addToSet(Se,e.ADD_TAGS,lt)),e.ADD_ATTR&&(Ee===Ae&&(Ee=clone(Ee)),addToSet(Ee,e.ADD_ATTR,lt)),e.ADD_URI_SAFE_ATTR&&addToSet(qe,e.ADD_URI_SAFE_ATTR,lt),e.FORBID_CONTENTS&&(We===Ye&&(We=clone(We)),addToSet(We,e.FORBID_CONTENTS,lt)),Fe&&(Se["#text"]=!0),ve&&addToSet(Se,["html","head","body"]),Se.table&&(addToSet(Se,["tbody"]),delete Ne.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ne=e.TRUSTED_TYPES_POLICY,oe=ne.createHTML("")}else void 0===ne&&(ne=function _createTrustedTypesPolicy(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+a+" could not be created."),null}}(F,a)),null!==ne&&"string"==typeof oe&&(oe=ne.createHTML(""));i&&i(e),ct=e}},dt=addToSet({},[...R,...k,...O]),mt=addToSet({},[...x,...D]),ft=function _forceRemove(e){f(DOMPurify.removed,{element:e});try{te(e).removeChild(e)}catch(t){j(e)}},ht=function _removeAttribute(e,t){try{f(DOMPurify.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){f(DOMPurify.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Ie||ze)try{ft(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Tt=function _initDocument(e){let t=null,o=null;if(Me)e="<remove></remove>"+e;else{const t=y(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===rt&&Je===Ze&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const a=ne?ne.createHTML(e):e;if(Je===Ze)try{t=(new H).parseFromString(a,rt)}catch(e){}if(!t||!t.documentElement){t=ae.createDocument(Je,"template",null);try{t.documentElement.innerHTML=Qe?oe:a}catch(e){}}const r=t.body||t.documentElement;return e&&o&&r.insertBefore(n.createTextNode(o),r.childNodes[0]||null),Je===Ze?le.call(t,ve?"html":"body")[0]:ve?t.documentElement:r},gt=function _createNodeIterator(e){return re.call(e.ownerDocument||e,e,z.SHOW_ELEMENT|z.SHOW_COMMENT|z.SHOW_TEXT|z.SHOW_PROCESSING_INSTRUCTION|z.SHOW_CDATA_SECTION,null)},yt=function _isClobbered(e){return e instanceof U&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof P)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},St=function _isNode(e){return"function"==typeof s&&e instanceof s};function _executeHooks(e,t,n){p(e,(e=>{e.call(DOMPurify,t,n,ct)}))}const _t=function _sanitizeElements(e){let t=null;if(_executeHooks(se.beforeSanitizeElements,e,null),yt(e))return ft(e),!0;const n=lt(e.nodeName);if(_executeHooks(se.uponSanitizeElement,e,{tagName:n,allowedTags:Se}),e.hasChildNodes()&&!St(e.firstElementChild)&&b(/<[/\w!]/g,e.innerHTML)&&b(/<[/\w!]/g,e.textContent))return ft(e),!0;if(e.nodeType===$)return ft(e),!0;if(Ce&&e.nodeType===K&&b(/<[/\w]/g,e.data))return ft(e),!0;if(!Se[n]||Ne[n]){if(!Ne[n]&&At(n)){if(be.tagNameCheck instanceof RegExp&&b(be.tagNameCheck,n))return!1;if(be.tagNameCheck instanceof Function&&be.tagNameCheck(n))return!1}if(Fe&&!We[n]){const t=te(e)||e.parentNode,n=ee(e)||e.childNodes;if(n&&t){for(let o=n.length-1;o>=0;--o){const a=W(n[o],!0);a.__removalCount=(e.__removalCount||0)+1,t.insertBefore(a,Q(e))}}}return ft(e),!0}return e instanceof u&&!function _checkValidNamespace(e){let t=te(e);t&&t.tagName||(t={namespaceURI:Je,tagName:"template"});const n=T(e.tagName),o=T(t.tagName);return!!et[e.namespaceURI]&&(e.namespaceURI===Ke?t.namespaceURI===Ze?"svg"===n:t.namespaceURI===$e?"svg"===n&&("annotation-xml"===o||nt[o]):Boolean(dt[n]):e.namespaceURI===$e?t.namespaceURI===Ze?"math"===n:t.namespaceURI===Ke?"math"===n&&ot[o]:Boolean(mt[n]):e.namespaceURI===Ze?!(t.namespaceURI===Ke&&!ot[o])&&!(t.namespaceURI===$e&&!nt[o])&&!mt[n]&&(at[n]||!dt[n]):!("application/xhtml+xml"!==rt||!et[e.namespaceURI]))}(e)?(ft(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!b(/<\/no(script|embed|frames)/i,e.innerHTML)?(De&&e.nodeType===V&&(t=e.textContent,p([ue,pe,de],(e=>{t=S(t,e," ")})),e.textContent!==t&&(f(DOMPurify.removed,{element:e.cloneNode()}),e.textContent=t)),_executeHooks(se.afterSanitizeElements,e,null),!1):(ft(e),!0)},Et=function _isValidAttribute(e,t,o){if(Ue&&("id"===t||"name"===t)&&(o in n||o in st))return!1;if(ke&&!we[t]&&b(me,t));else if(Re&&b(fe,t));else if(!Ee[t]||we[t]){if(!(At(e)&&(be.tagNameCheck instanceof RegExp&&b(be.tagNameCheck,e)||be.tagNameCheck instanceof Function&&be.tagNameCheck(e))&&(be.attributeNameCheck instanceof RegExp&&b(be.attributeNameCheck,t)||be.attributeNameCheck instanceof Function&&be.attributeNameCheck(t))||"is"===t&&be.allowCustomizedBuiltInElements&&(be.tagNameCheck instanceof RegExp&&b(be.tagNameCheck,o)||be.tagNameCheck instanceof Function&&be.tagNameCheck(o))))return!1}else if(qe[t]);else if(b(ye,S(o,Te,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==_(o,"data:")||!je[e]){if(Oe&&!b(he,S(o,Te,"")));else if(o)return!1}else;return!0},At=function _isBasicCustomElement(e){return"annotation-xml"!==e&&y(e,ge)},bt=function _sanitizeAttributes(e){_executeHooks(se.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||yt(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Ee,forceKeepAttr:void 0};let o=t.length;for(;o--;){const a=t[o],{name:r,namespaceURI:i,value:l}=a,c=lt(r);let s="value"===r?l:E(l);if(n.attrName=c,n.attrValue=s,n.keepAttr=!0,n.forceKeepAttr=void 0,_executeHooks(se.uponSanitizeAttribute,e,n),s=n.attrValue,!He||"id"!==c&&"name"!==c||(ht(r,e),s="user-content-"+s),Ce&&b(/((--!?|])>)|<\/(style|title)/i,s)){ht(r,e);continue}if(n.forceKeepAttr)continue;if(ht(r,e),!n.keepAttr)continue;if(!xe&&b(/\/>/i,s)){ht(r,e);continue}De&&p([ue,pe,de],(e=>{s=S(s,e," ")}));const u=lt(e.nodeName);if(Et(u,c,s)){if(ne&&"object"==typeof F&&"function"==typeof F.getAttributeType)if(i);else switch(F.getAttributeType(u,c)){case"TrustedHTML":s=ne.createHTML(s);break;case"TrustedScriptURL":s=ne.createScriptURL(s)}try{i?e.setAttributeNS(i,r,s):e.setAttribute(r,s),yt(e)?ft(e):m(DOMPurify.removed)}catch(e){}}}_executeHooks(se.afterSanitizeAttributes,e,null)},Nt=function _sanitizeShadowDOM(e){let t=null;const n=gt(e);for(_executeHooks(se.beforeSanitizeShadowDOM,e,null);t=n.nextNode();)_executeHooks(se.uponSanitizeShadowNode,t,null),_t(t),bt(t),t.content instanceof r&&_sanitizeShadowDOM(t.content);_executeHooks(se.afterSanitizeShadowDOM,e,null)};return DOMPurify.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,a=null,i=null,l=null;if(Qe=!e,Qe&&(e="\x3c!--\x3e"),"string"!=typeof e&&!St(e)){if("function"!=typeof e.toString)throw N("toString is not a function");if("string"!=typeof(e=e.toString()))throw N("dirty is not a string, aborting")}if(!DOMPurify.isSupported)return e;if(Le||pt(t),DOMPurify.removed=[],"string"==typeof e&&(Ge=!1),Ge){if(e.nodeName){const t=lt(e.nodeName);if(!Se[t]||Ne[t])throw N("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof s)n=Tt("\x3c!----\x3e"),a=n.ownerDocument.importNode(e,!0),a.nodeType===q&&"BODY"===a.nodeName||"HTML"===a.nodeName?n=a:n.appendChild(a);else{if(!Ie&&!De&&!ve&&-1===e.indexOf("<"))return ne&&Pe?ne.createHTML(e):e;if(n=Tt(e),!n)return Ie?null:Pe?oe:""}n&&Me&&ft(n.firstChild);const c=gt(Ge?e:n);for(;i=c.nextNode();)_t(i),bt(i),i.content instanceof r&&Nt(i.content);if(Ge)return e;if(Ie){if(ze)for(l=ie.call(n.ownerDocument);n.firstChild;)l.appendChild(n.firstChild);else l=n;return(Ee.shadowroot||Ee.shadowrootmode)&&(l=ce.call(o,l,!0)),l}let u=ve?n.outerHTML:n.innerHTML;return ve&&Se["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&b(Y,n.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+u),De&&p([ue,pe,de],(e=>{u=S(u,e," ")})),ne&&Pe?ne.createHTML(u):u},DOMPurify.setConfig=function(){pt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Le=!0},DOMPurify.clearConfig=function(){ct=null,Le=!1},DOMPurify.isValidAttribute=function(e,t,n){ct||pt({});const o=lt(e),a=lt(t);return Et(o,a,n)},DOMPurify.addHook=function(e,t){"function"==typeof t&&f(se[e],t)},DOMPurify.removeHook=function(e,t){if(void 0!==t){const n=d(se[e],t);return-1===n?void 0:h(se[e],n,1)[0]}return m(se[e])},DOMPurify.removeHooks=function(e){se[e]=[]},DOMPurify.removeAllHooks=function(){se={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},DOMPurify}();e.exports=Q}}]);