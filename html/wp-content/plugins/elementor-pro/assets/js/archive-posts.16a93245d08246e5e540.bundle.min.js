/*! elementor-pro - v3.29.0 - 19-05-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[345,396],{7907:(e,t,s)=>{var n=s(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(s(2195));t.default=o.default.extend({getSkinPrefix:()=>"cards_"})},2078:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class LoadMore extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{postsContainer:".elementor-posts-container",postWrapperTag:"article",loadMoreButton:".elementor-button",loadMoreSpinnerWrapper:".e-load-more-spinner",loadMoreSpinner:".e-load-more-spinner i, .e-load-more-spinner svg",loadMoreAnchor:".e-load-more-anchor"},classes:{loadMoreSpin:"eicon-animation-spin",loadMoreIsLoading:"e-load-more-pagination-loading",loadMorePaginationEnd:"e-load-more-pagination-end",loadMoreNoSpinner:"e-load-more-no-spinner"}}}getDefaultElements(){const e=this.getSettings("selectors");return{postsWidgetWrapper:this.$element[0],postsContainer:this.$element[0].querySelector(e.postsContainer),loadMoreButton:this.$element[0].querySelector(e.loadMoreButton),loadMoreSpinnerWrapper:this.$element[0].querySelector(e.loadMoreSpinnerWrapper),loadMoreSpinner:this.$element[0].querySelector(e.loadMoreSpinner),loadMoreAnchor:this.$element[0].querySelector(e.loadMoreAnchor)}}bindEvents(){super.bindEvents(),this.elements.loadMoreButton&&this.elements.loadMoreButton.addEventListener("click",(e=>{this.isLoading||(e.preventDefault(),this.handlePostsQuery())}))}onInit(){super.onInit(),this.classes=this.getSettings("classes"),this.isLoading=!1;const e=this.getElementSettings("pagination_type");"load_more_on_click"!==e&&"load_more_infinite_scroll"!==e||(this.isInfinteScroll="load_more_infinite_scroll"===e,this.isSpinnerAvailable=this.getElementSettings("load_more_spinner").value,this.isSpinnerAvailable||this.elements.postsWidgetWrapper.classList.add(this.classes.loadMoreNoSpinner),this.isInfinteScroll?this.handleInfiniteScroll():this.elements.loadMoreSpinnerWrapper&&this.elements.loadMoreButton&&this.elements.loadMoreButton.insertAdjacentElement("beforeEnd",this.elements.loadMoreSpinnerWrapper),this.elementId=this.getID(),this.postId=elementorFrontendConfig.post.id,this.elements.loadMoreAnchor&&(this.currentPage=parseInt(this.elements.loadMoreAnchor.getAttribute("data-page")),this.maxPage=parseInt(this.elements.loadMoreAnchor.getAttribute("data-max-page")),this.currentPage!==this.maxPage&&this.currentPage||this.handleUiWhenNoPosts()))}handleInfiniteScroll(){this.isEdit||(this.observer=elementorModules.utils.Scroll.scrollObserver({callback:e=>{e.isInViewport&&!this.isLoading&&(this.observer.unobserve(this.elements.loadMoreAnchor),this.handlePostsQuery().then((()=>{this.currentPage!==this.maxPage&&this.observer.observe(this.elements.loadMoreAnchor)})))}}),this.observer.observe(this.elements.loadMoreAnchor))}handleUiBeforeLoading(){this.isLoading=!0,this.elements.loadMoreSpinner&&this.elements.loadMoreSpinner.classList.add(this.classes.loadMoreSpin),this.elements.postsWidgetWrapper.classList.add(this.classes.loadMoreIsLoading)}handleUiAfterLoading(){this.isLoading=!1,this.elements.loadMoreSpinner&&this.elements.loadMoreSpinner.classList.remove(this.classes.loadMoreSpin),this.isInfinteScroll&&this.elements.loadMoreSpinnerWrapper&&this.elements.loadMoreAnchor&&this.elements.loadMoreAnchor.insertAdjacentElement("afterend",this.elements.loadMoreSpinnerWrapper),this.elements.postsWidgetWrapper.classList.remove(this.classes.loadMoreIsLoading)}handleUiWhenNoPosts(){this.elements.postsWidgetWrapper.classList.add(this.classes.loadMorePaginationEnd)}afterInsertPosts(){}handleSuccessFetch(e){this.handleUiAfterLoading();const t=this.getSettings("selectors"),s=e.querySelectorAll(`[data-id="${this.elementId}"] ${t.postsContainer} > ${t.postWrapperTag}`),n=e.querySelector(`[data-id="${this.elementId}"] .e-load-more-anchor`).getAttribute("data-next-page");s.forEach((e=>this.elements.postsContainer.append(e))),this.elements.loadMoreAnchor.setAttribute("data-page",this.currentPage),this.elements.loadMoreAnchor.setAttribute("data-next-page",n),this.currentPage===this.maxPage&&this.handleUiWhenNoPosts(),this.afterInsertPosts(s,e)}handlePostsQuery(){this.handleUiBeforeLoading(),this.currentPage++;const e=this.elements.loadMoreAnchor.getAttribute("data-next-page");return fetch(e).then((e=>e.text())).then((e=>{const t=(new DOMParser).parseFromString(e,"text/html");this.handleSuccessFetch(t)}))}}t.default=LoadMore},2195:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=elementorModules.frontend.handlers.Base.extend({getSkinPrefix:()=>"classic_",bindEvents(){elementorFrontend.addListenerOnce(this.getModelCID(),"resize",this.onWindowResize)},unbindEvents(){elementorFrontend.removeListeners(this.getModelCID(),"resize",this.onWindowResize)},getClosureMethodsNames(){return elementorModules.frontend.handlers.Base.prototype.getClosureMethodsNames.apply(this,arguments).concat(["fitImages","onWindowResize","runMasonry"])},getDefaultSettings:()=>({classes:{fitHeight:"elementor-fit-height",hasItemRatio:"elementor-has-item-ratio"},selectors:{postsContainer:".elementor-posts-container",post:".elementor-post",postThumbnail:".elementor-post__thumbnail",postThumbnailImage:".elementor-post__thumbnail img"}}),getDefaultElements(){var e=this.getSettings("selectors");return{$postsContainer:this.$element.find(e.postsContainer),$posts:this.$element.find(e.post)}},fitImage(e){var t=this.getSettings(),s=e.find(t.selectors.postThumbnail),n=s.find("img")[0];if(n){var o=s.outerHeight()/s.outerWidth(),i=n.naturalHeight/n.naturalWidth;s.toggleClass(t.classes.fitHeight,i<o)}},fitImages(){var e=jQuery,t=this,s=getComputedStyle(this.$element[0],":after").content,n=this.getSettings();t.isMasonryEnabled()?this.elements.$postsContainer.removeClass(n.classes.hasItemRatio):(this.elements.$postsContainer.toggleClass(n.classes.hasItemRatio,!!s.match(/\d/)),this.elements.$posts.each((function(){var s=e(this),o=s.find(n.selectors.postThumbnailImage);t.fitImage(s),o.on("load",(function(){t.fitImage(s)}))})))},setColsCountSettings(){const e=this.getElementSettings(),t=this.getSkinPrefix(),s=elementorProFrontend.utils.controls.getResponsiveControlValue(e,`${t}columns`);this.setSettings("colsCount",s)},isMasonryEnabled(){return!!this.getElementSettings(this.getSkinPrefix()+"masonry")},initMasonry(){imagesLoaded(this.elements.$posts,this.runMasonry)},getVerticalSpaceBetween(){let e=elementorProFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),`${this.getSkinPrefix()}row_gap`,"size");return""===this.getSkinPrefix()&&""===e&&(e=this.getElementSettings("item_gap.size")),e},runMasonry(){var e=this.elements;e.$posts.css({marginTop:"",transitionDuration:""}),this.setColsCountSettings();var t=this.getSettings("colsCount"),s=this.isMasonryEnabled()&&t>=2;if(e.$postsContainer.toggleClass("elementor-posts-masonry",s),!s)return void e.$postsContainer.height("");const n=this.getVerticalSpaceBetween();new elementorModules.utils.Masonry({container:e.$postsContainer,items:e.$posts.filter(":visible"),columnsCount:this.getSettings("colsCount"),verticalSpaceBetween:n||0}).run()},run(){setTimeout(this.fitImages,0),this.initMasonry()},onInit(){elementorModules.frontend.handlers.Base.prototype.onInit.apply(this,arguments),this.bindEvents(),this.run()},onWindowResize(){this.fitImages(),this.runMasonry()},onElementChange(){this.fitImages(),setTimeout(this.runMasonry)}})},439:(e,t,s)=>{var n=s(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(s(2078));class ArchivePostsLoadMore extends o.default{}t.default=ArchivePostsLoadMore},2718:(e,t,s)=>{var n=s(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(s(7907));t.default=o.default.extend({getSkinPrefix:()=>"archive_cards_"})},6629:(e,t,s)=>{var n=s(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(s(2195));t.default=o.default.extend({getSkinPrefix:()=>"archive_classic_"})}}]);