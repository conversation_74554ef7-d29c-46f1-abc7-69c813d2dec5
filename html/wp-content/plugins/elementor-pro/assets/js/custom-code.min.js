/*! elementor-pro - v3.29.0 - 19-05-2025 */
(()=>{var e={9804:(e,t,n)=>{"use strict";var o=n(1594);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.BaseContext=void 0;class BaseContext extends o.Component{constructor(e){super(e),this.state={action:{current:null,loading:!1,error:null,errorMeta:{}},updateActionState:this.updateActionState.bind(this),resetActionState:this.resetActionState.bind(this)}}executeAction(e,t){return this.updateActionState({current:e,loading:!0,error:null,errorMeta:{}}),t().then((e=>(this.resetActionState(),Promise.resolve(e)))).catch((t=>(this.updateActionState({current:e,loading:!1,error:t.message,errorMeta:t}),Promise.reject(t))))}updateActionState(e){return this.setState((t=>({action:{...t.action,...e}})))}resetActionState(){this.updateActionState({current:null,loading:!1,error:null,errorMeta:{}})}}t.BaseContext=BaseContext;t.default=BaseContext},4737:(e,t,n)=>{"use strict";var o=n(2688),r=n(2470).__,i=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.Context=t.ConditionsProvider=void 0;var s=i(n(1594)),a=i(n(8067)),u=i(n(2075)),c=i(n(9804)),l=n(5559);const d=t.Context=s.default.createContext();class ConditionsProvider extends c.default{static propTypes=(()=>({children:o.any.isRequired,currentTemplate:o.object.isRequired,onConditionsSaved:o.func.isRequired,validateConflicts:o.bool}))();static defaultProps={validateConflicts:!0};static actions={FETCH_CONFIG:"fetch-config",SAVE:"save",CHECK_CONFLICTS:"check-conflicts"};conditionsConfig=null;constructor(e){super(e),this.state={...this.state,conditionsFetched:!1,conditions:{},updateConditionItemState:this.updateConditionItemState.bind(this),removeConditionItemInState:this.removeConditionItemInState.bind(this),createConditionItemInState:this.createConditionItemInState.bind(this),findConditionItemInState:this.findConditionItemInState.bind(this),saveConditions:this.saveConditions.bind(this)}}componentDidMount(){this.executeAction(ConditionsProvider.actions.FETCH_CONFIG,(()=>u.default.create())).then((e=>this.conditionsConfig=e)).then(this.normalizeConditionsState.bind(this)).then((()=>{this.setSubIdTitles.bind(this),this.setState({conditionsFetched:!0})}))}componentDidUpdate(e,t){!t.conditionsFetched&&this.state.conditionsFetched&&this.setSubIdTitles()}saveConditions(){const e=Object.values(this.state.conditions).map((e=>e.forDb()));return this.executeAction(ConditionsProvider.actions.SAVE,(()=>$e.data.update(l.TemplatesConditions.signature,{conditions:e},{id:this.props.currentTemplate.id}))).then((()=>{const e=Object.values(this.state.conditions).map((e=>e.forContext()));this.props.onConditionsSaved(this.props.currentTemplate.id,{conditions:e,instances:this.conditionsConfig.calculateInstances(Object.values(this.state.conditions)),isActive:!(!Object.keys(this.state.conditions).length||"publish"!==this.props.currentTemplate.status)})}))}checkConflicts(e){return this.executeAction(ConditionsProvider.actions.CHECK_CONFLICTS,(()=>$e.data.get(l.TemplatesConditionsConflicts.signature,{post_id:this.props.currentTemplate.id,condition:e.clone().toString()}))).then((t=>this.updateConditionItemState(e.id,{conflictErrors:Object.values(t.data)},!1)))}fetchSubIdsTitles(e){return new Promise((t=>elementorCommon.ajax.loadObjects({action:"query_control_value_titles",ids:_.isArray(e.subId)?e.subId:[e.subId],data:{get_titles:e.subIdAutocomplete,unique_id:elementorCommon.helpers.getUniqueId()},success(e){t(e)}})))}normalizeConditionsState(){this.updateConditionsState((()=>this.props.currentTemplate.conditions.reduce(((e,t)=>{const n=new a.default({...t,default:this.props.currentTemplate.defaultCondition,options:this.conditionsConfig.getOptions(),subOptions:this.conditionsConfig.getSubOptions(t.name),subIdAutocomplete:this.conditionsConfig.getSubIdAutocomplete(t.sub),subIdOptions:t.subId?[{value:t.subId,label:""}]:[]});return{...e,[n.id]:n}}),{}))).then((()=>{Object.values(this.state.conditions).forEach((e=>this.checkConflicts(e)))}))}setSubIdTitles(){return Object.values(this.state.conditions).forEach((e=>{if(e.subId)return this.fetchSubIdsTitles(e).then((t=>this.updateConditionItemState(e.id,{subIdOptions:[{label:Object.values(t)[0],value:e.subId}]},!1)))}))}updateConditionItemState(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t.name&&(t.subOptions=this.conditionsConfig.getSubOptions(t.name)),(t.sub||t.name)&&(t.subIdAutocomplete=this.conditionsConfig.getSubIdAutocomplete(t.sub),t.subIdOptions=[]),this.updateConditionsState((n=>{const o=n[e];return{...n,[e]:o.clone().set(t)}})).then((()=>{n&&this.checkConflicts(this.findConditionItemInState(e))}))}removeConditionItemInState(e){this.updateConditionsState((t=>{const n={...t};return delete n[e],n}))}createConditionItemInState(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=this.props.currentTemplate.defaultCondition,n=new a.default({name:t,default:t,options:this.conditionsConfig.getOptions(),subOptions:this.conditionsConfig.getSubOptions(t),subIdAutocomplete:this.conditionsConfig.getSubIdAutocomplete("")});this.updateConditionsState((e=>({...e,[n.id]:n}))).then((()=>{e&&this.checkConflicts(n)}))}findConditionItemInState(e){return Object.values(this.state.conditions).find((t=>t.id===e))}updateConditionsState(e){return new Promise((t=>this.setState((t=>({conditions:e(t.conditions)})),t)))}render(){if(this.state.action.current===ConditionsProvider.actions.FETCH_CONFIG){if(this.state.error)return s.default.createElement("h3",null,r("Error:","elementor-pro")," ",this.state.error);if(this.state.loading)return s.default.createElement("h3",null,r("Loading","elementor-pro"),"...")}return s.default.createElement(d.Provider,{value:this.state},this.props.children)}}t.ConditionsProvider=ConditionsProvider;t.default=ConditionsProvider},8067:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n(6281);class Condition{id=(()=>elementorCommon.helpers.getUniqueId())();default="";type="include";name="";sub="";subId="";options=[];subOptions=[];subIdAutocomplete=[];subIdOptions=[];conflictErrors=[];constructor(e){this.set(e)}set(e){return Object.assign(this,e),this}clone(){return Object.assign(new Condition,this)}remove(e){return Array.isArray(e)||(e=[e]),e.forEach((e=>{delete this[e]})),this}only(e){Array.isArray(e)||(e=[e]);const t=Object.keys(this).filter((t=>!e.includes(t)));return this.remove(t),this}toJson(){return JSON.stringify(this)}toString(){return this.forDb().filter((e=>e)).join("/")}forDb(){return[this.type,this.name,this.sub,this.subId]}forContext(){return{type:this.type,name:this.name,sub:this.sub,subId:this.subId}}}t.default=Condition},2075:(e,t,n)=>{"use strict";var o=n(2470).__;Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.ConditionsConfig=void 0;var r=n(5559);class ConditionsConfig{static instance;config=null;constructor(e){this.config=e}static create(){return ConditionsConfig.instance?Promise.resolve(ConditionsConfig.instance):$e.data.get(r.ConditionsConfig.signature,{},{refresh:!0}).then((e=>(ConditionsConfig.instance=new ConditionsConfig(e.data),ConditionsConfig.instance)))}getOptions(){return this.getSubOptions("general",!0).map((e=>{let{label:t,value:n}=e;return{label:t,value:n}}))}getSubOptions(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.config[e];return n?[{label:n.all_label,value:t?e:""},...n.sub_conditions.map((e=>{const t=this.config[e];return{label:t.label,value:e,children:t.sub_conditions.length?this.getSubOptions(e,!0):null}}))]:[]}getSubIdAutocomplete(e){const t=this.config[e];if(!t||"object"!=typeof t.controls)return{};const n=Object.values(t.controls);return n?.[0]?.autocomplete?n[0].autocomplete:{}}calculateInstances(e){let t=e.reduce(((e,t)=>{if("exclude"===t.type)return e;const n=t.sub||t.name,o=this.config[n];if(!o)return e;const r=t.subId?`${o.label} #${t.subId}`:o.all_label;return{...e,[n]:r}}),{});return 0===Object.keys(t).length&&(t=[o("No instances","elementor-pro")]),t}}t.ConditionsConfig=ConditionsConfig;t.default=ConditionsConfig},7952:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.ConditionsConfig=void 0;class ConditionsConfig extends $e.modules.CommandData{static signature="site-editor/conditions-config";static getEndpointFormat(){return"site-editor/conditions-config/{id}"}}t.ConditionsConfig=ConditionsConfig;t.default=ConditionsConfig},5559:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ConditionsConfig",{enumerable:!0,get:function(){return r.ConditionsConfig}}),Object.defineProperty(t,"Templates",{enumerable:!0,get:function(){return o.Templates}}),Object.defineProperty(t,"TemplatesConditions",{enumerable:!0,get:function(){return i.TemplatesConditions}}),Object.defineProperty(t,"TemplatesConditionsConflicts",{enumerable:!0,get:function(){return s.TemplatesConditionsConflicts}});var o=n(7636),r=n(7952),i=n(9591),s=n(7821)},7821:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.TemplatesConditionsConflicts=void 0;class TemplatesConditionsConflicts extends $e.modules.CommandData{static signature="site-editor/templates-conditions-conflicts";static getEndpointFormat(){return`${TemplatesConditionsConflicts.signature}/{id}`}}t.TemplatesConditionsConflicts=TemplatesConditionsConflicts;t.default=TemplatesConditionsConflicts},9591:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.TemplatesConditions=void 0;class TemplatesConditions extends $e.modules.CommandData{static signature="site-editor/templates-conditions";static getEndpointFormat(){return"site-editor/templates-conditions/{id}"}}t.TemplatesConditions=TemplatesConditions;t.default=TemplatesConditions},7636:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.Templates=void 0;class Templates extends $e.modules.CommandData{static signature="site-editor/templates";static getEndpointFormat(){return"site-editor/templates/{id}"}}t.Templates=Templates;t.default=Templates},2239:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var s=r?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(o,i,s):o[i]=e[i]}return o.default=e,n&&n.set(e,o),o}(n(5559));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function(e){return e?n:t})(e)}class Component extends $e.modules.ComponentBase{static namespace="site-editor";getNamespace(){return this.constructor.namespace}defaultData(){return this.importCommands(o)}}t.default=Component},7010:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(5206),r=n(1594),i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var s=r?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(o,i,s):o[i]=e[i]}return o.default=e,n&&n.set(e,o),o}(n(2688));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function(e){return e?n:t})(e)}const ConditionButtonPortal=e=>{const[t,n]=(0,r.useState)(!1),i=document.getElementById("portal-root");return(0,r.useEffect)((()=>{n(!!i)}),[i]),t?(0,o.createPortal)(e.children,i):null};ConditionButtonPortal.propTypes={children:i.oneOfType([i.node,i.string])};t.default=ConditionButtonPortal},7624:(e,t,n)=>{"use strict";var o=n(2470).__,r=n(2688),i=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ConditionConflicts;var s=i(n(1594)),a=n(7401);function ConditionConflicts(e){if(!e.conflicts.length)return"";const t=e.conflicts.map((e=>s.default.createElement(a.Button,{key:e.template_id,target:"_blank",url:e.edit_url,text:e.template_title})));return s.default.createElement(a.Text,{className:"e-site-editor-conditions__conflict",variant:"sm"},o("Elementor recognized that you have set this location for other templates: ","elementor-pro")," ",t)}ConditionConflicts.propTypes={conflicts:r.array.isRequired}},1360:(e,t,n)=>{"use strict";var o=n(2688),r=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ConditionName;var i=r(n(1594)),s=n(7401);function ConditionName(e){if("general"!==e.default)return"";return i.default.createElement("div",{className:"e-site-editor-conditions__input-wrapper"},i.default.createElement(s.Select,{options:e.options,value:e.name,onChange:t=>e.updateConditions(e.id,{name:t.target.value,sub:"",subId:""})}))}ConditionName.propTypes={updateConditions:o.func.isRequired,id:o.string.isRequired,name:o.string.isRequired,options:o.array.isRequired,default:o.string.isRequired},ConditionName.defaultProps={name:""}},2943:(e,t,n)=>{"use strict";var o=n(2470).__,r=n(2688),i=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ConditionSubId;var s=i(n(1594)),a=n(7401);function ConditionSubId(e){const t=s.default.useMemo((()=>Object.keys(e.subIdAutocomplete).length?function getSettings(e){return{allowClear:!1,placeholder:o("All","elementor-pro"),dir:elementorCommon.config.isRTL?"rtl":"ltr",ajax:{transport:(t,n,o)=>elementorCommon.ajax.addRequest("pro_panel_posts_control_filter_autocomplete",{data:{q:t.data.q,autocomplete:e},success:n,error:o}),data:e=>({q:e.term,page:e.page}),cache:!0},escapeMarkup:e=>e,minimumInputLength:1}}(e.subIdAutocomplete):null),[e.subIdAutocomplete]);if(!e.sub||!t)return"";return s.default.createElement("div",{className:"e-site-editor-conditions__input-wrapper"},s.default.createElement(a.Select2,{onChange:t=>e.updateConditions(e.id,{subId:t.target.value}),value:e.subId,settings:t,options:e.subIdOptions}))}ConditionSubId.propTypes={subIdAutocomplete:r.object,id:r.string.isRequired,sub:r.string,subId:r.string,updateConditions:r.func,subIdOptions:r.array},ConditionSubId.defaultProps={subId:"",subIdOptions:[]}},2437:(e,t,n)=>{"use strict";var o=n(2688),r=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ConditionSub;var i=r(n(1594)),s=n(7401);function ConditionSub(e){if("general"===e.name||!e.subOptions.length)return"";return i.default.createElement("div",{className:"e-site-editor-conditions__input-wrapper"},i.default.createElement(s.Select,{options:e.subOptions,value:e.sub,onChange:t=>e.updateConditions(e.id,{sub:t.target.value,subId:""})}))}ConditionSub.propTypes={updateConditions:o.func.isRequired,id:o.string.isRequired,name:o.string.isRequired,sub:o.string.isRequired,subOptions:o.array.isRequired},ConditionSub.defaultProps={sub:"",subOptions:{}}},1303:(e,t,n)=>{"use strict";var o=n(2470).__,r=n(2688),i=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ConditionType;var s=i(n(1594)),a=n(7401);function ConditionType(e){const t=s.default.createRef(),n=[{label:o("Include","elementor-pro"),value:"include"},{label:o("Exclude","elementor-pro"),value:"exclude"}];return s.default.useEffect((()=>{t.current.setAttribute("data-elementor-condition-type",e.type)})),s.default.createElement("div",{className:"e-site-editor-conditions__input-wrapper e-site-editor-conditions__input-wrapper--condition-type",ref:t},s.default.createElement(a.Select,{options:n,value:e.type,onChange:t=>{e.updateConditions(e.id,{type:t.target.value})}}))}ConditionType.propTypes={updateConditions:r.func.isRequired,id:r.string.isRequired,type:r.string.isRequired},ConditionType.defaultProps={type:""}},8927:(e,t,n)=>{"use strict";var o=n(2470).__,r=n(2688),i=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ConditionsRows;var s=i(n(1594)),a=i(n(8304)),u=n(4737),c=n(7401),l=i(n(1303)),d=i(n(1360)),p=i(n(2437)),f=i(n(2943)),m=i(n(7624)),h=i(n(7010));function ConditionsRows(e){const{conditions:t,createConditionItemInState:n,updateConditionItemState:r,removeConditionItemInState:i,saveConditions:v,action:b,resetActionState:g}=s.default.useContext(u.Context),C=Object.values(t).map((e=>s.default.createElement("div",{key:e.id},s.default.createElement("div",{className:"e-site-editor-conditions__row"},s.default.createElement("div",{className:`e-site-editor-conditions__row-controls ${e.conflictErrors.length&&"e-site-editor-conditions__row-controls--error"}`},s.default.createElement(l.default,(0,a.default)({},e,{updateConditions:r})),s.default.createElement("div",{className:"e-site-editor-conditions__row-controls-inner"},s.default.createElement(d.default,(0,a.default)({},e,{updateConditions:r})),s.default.createElement(p.default,(0,a.default)({},e,{updateConditions:r})),s.default.createElement(f.default,(0,a.default)({},e,{updateConditions:r})))),s.default.createElement(c.Button,{className:"e-site-editor-conditions__remove-condition",text:o("Delete","elementor-pro"),icon:"eicon-close",hideText:!0,onClick:()=>i(e.id)})),s.default.createElement(m.default,{conflicts:e.conflictErrors})))),SaveButton=()=>s.default.createElement(c.Button,{variant:"contained",color:"primary",size:"lg",hideText:y,icon:y?"eicon-loading eicon-animation-spin":"",text:o("Save & Close","elementor-pro"),onClick:()=>v().then(e.onAfterSave)}),y=b.current===u.ConditionsProvider.actions.SAVE&&b.loading;return s.default.createElement(s.default.Fragment,null,b.error&&s.default.createElement(c.Dialog,{text:b.error,dismissButtonText:o("Go Back","elementor-pro"),dismissButtonOnClick:g,approveButtonText:o("Learn More","elementor-pro"),approveButtonColor:"link",approveButtonUrl:"https://go.elementor.com/app-theme-builder-conditions-load-issue",approveButtonTarget:"_target"}),s.default.createElement("div",{className:"e-site-editor-conditions__rows"},C),s.default.createElement("div",{className:"e-site-editor-conditions__add-button-container"},s.default.createElement(c.Button,{className:"e-site-editor-conditions__add-button",variant:"contained",size:"lg",text:o("Add Condition","elementor-pro"),onClick:n})),s.default.createElement("div",{className:"e-site-editor-conditions__footer"},e?.loadPortal?s.default.createElement(h.default,null,s.default.createElement(SaveButton,null)):s.default.createElement(SaveButton,null)))}ConditionsRows.propTypes={onAfterSave:r.func,loadPortal:r.bool}},9212:(e,t,n)=>{"use strict";var o=n(2470).__,r=n(2688),i=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ConditionsModal;var s=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var s=r?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(o,i,s):o[i]=e[i]}return o.default=e,n&&n.set(e,o),o}(n(1594)),a=n(7401),u=i(n(12)),c=i(n(2075));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function(e){return e?n:t})(e)}function ConditionsModal(){const[e,t]=(0,s.useState)(!1),[n,r]=(0,s.useState)({conditions:null,instances:null}),i=(0,s.useRef)(!1),l=elementorProAdmin.customCode.post,d=(0,s.useMemo)((()=>({$form:jQuery("#post"),$formConditions:jQuery("<input />"),$publishButton:jQuery("#publish"),title:{$label:jQuery("#title-prompt-text"),$input:jQuery("#title")}})),[]),onPostSubmit=()=>{const{title:e}=d;e.$input.attr("value").length||(e.$label.addClass("screen-reader-text"),e.$input.attr("value",o("Elementor Custom-Code #","elementor-pro")+elementorProAdmin.customCode.post.ID))},onPublishClick=n=>{if("auto-draft"===l.post_status&&!e&&!i.current){n.preventDefault();const e=[{name:"general",sub:"",subId:"",type:"include"}];r((t=>({...t,conditions:e}))),t(!0)}};return(0,s.useEffect)((()=>{(async()=>{const e=await c.default.create();$e.data.get("site-editor/templates-conditions",{id:l.ID},{refresh:!0}).then((t=>{const n=Object.values(t.data).map((e=>({type:e.type,name:e.name,sub:e.sub_name,subId:e.sub_id}))),o=Object.values(e.calculateInstances(n)).join(",");r((e=>({...e,conditions:n,instances:o})))}))})(),d.$publishButton.on("click",onPublishClick),d.$form.on("submit",onPostSubmit)}),[]),l&&n.conditions?s.default.createElement(s.default.Fragment,null,s.default.createElement(a.Text,{tag:"span",className:"post-conditions-display"},s.default.createElement("b",null,n.instances+" ")),s.default.createElement(a.Button,{onClick:()=>t(!0),text:o("Edit","elementor-pro"),variant:"underlined"}),s.default.createElement(a.ModalProvider,{show:e,setShow:t,title:o("Publish Settings","elementor-pro"),icon:"eps-app__logo eicon-elementor"},s.default.createElement(a.CssGrid,{columns:1,spacing:700},s.default.createElement("section",null,s.default.createElement(u.default,{id:l.ID,status:l.post_status,conditions:n.conditions,onConditionsSaved:e=>{const n=e.conditions,o=Object.values(e.instances).join(","),{$form:s,$formConditions:a,$publishButton:u}=d;i.current=!0,r((e=>({...e,conditions:n,instances:o}))),"auto-draft"!==l.post_status&&"draft"!==l.post_status||a.attr("type","hidden").attr("name","_conditions").attr("value",JSON.stringify(n)).appendTo(s),u.trigger("click"),t(!1)},onAfterSave:()=>{}}))))):s.default.createElement(s.default.Fragment,null,s.default.createElement(a.Text,{tag:"span"},o("Loading","elementor-pro")),s.default.createElement(a.Icon,{className:"spinner"}))}ConditionsModal.propTypes={children:r.object}},12:(e,t,n)=>{"use strict";var o=n(2470).__,r=n(2688),i=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Conditions;var s=i(n(1594)),a=n(7401),u=i(n(4737)),c=i(n(8927));function Conditions(e){const t={...e,defaultCondition:"general"};return s.default.createElement("section",{className:"e-site-editor-conditions"},s.default.createElement("div",{className:"e-site-editor-conditions__header"},s.default.createElement("img",{className:"e-site-editor-conditions__header-image",src:`${elementorAppProConfig.baseUrl}/modules/theme-builder/assets/images/conditions-tab.svg`,alt:o("Conditions","elementor-pro")}),s.default.createElement(a.Heading,{variant:"h1",tag:"h1"},o("Where Do You Want to Display Your Code?","elementor-pro")),s.default.createElement(a.Text,{variant:"md"},o("Set the conditions that determine where your code snippet is used throughout your site.","elementor-pro"),s.default.createElement("br",null),o("For example, choose 'Entire Site' to display the code snippet across your site.","elementor-pro"))),s.default.createElement(u.default,{validateConflicts:!1,currentTemplate:t,onConditionsSaved:(t,n)=>{$e.data.setCache($e.components.get("site-editor"),"site-editor/templates-conditions",{id:t},n.conditions),e.onConditionsSaved(n)}},s.default.createElement(c.default,{onAfterSave:e.onAfterSave,loadPortal:!1})))}Conditions.propTypes={id:r.number,status:r.string.isRequired,conditions:r.array,onConditionsSaved:r.func,onAfterSave:r.func.isRequired},Conditions.defaultProps={conditions:[],onConditionsSaved:()=>{}}},362:(e,t,n)=>{"use strict";var o=n(6441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,n,r,i,s){if(s!==o){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},2688:(e,t,n)=>{e.exports=n(362)()},6441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},1594:e=>{"use strict";e.exports=React},5206:e=>{"use strict";e.exports=ReactDOM},1755:e=>{"use strict";e.exports=__UNSTABLE__elementorAI.App},7401:e=>{"use strict";e.exports=elementorAppPackages.appUi},2470:e=>{"use strict";e.exports=wp.i18n},8304:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(null,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},6784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},8120:(e,t,n)=>{"use strict";var o=n(1483),r=n(8761),i=TypeError;e.exports=function(e){if(o(e))return e;throw new i(r(e)+" is not a function")}},7095:(e,t,n)=>{"use strict";var o=n(1),r=n(5290),i=n(5835).f,s=o("unscopables"),a=Array.prototype;void 0===a[s]&&i(a,s,{configurable:!0,value:r(null)}),e.exports=function(e){a[s][e]=!0}},2293:(e,t,n)=>{"use strict";var o=n(1704),r=String,i=TypeError;e.exports=function(e){if(o(e))return e;throw new i(r(e)+" is not an object")}},6651:(e,t,n)=>{"use strict";var o=n(5599),r=n(3392),i=n(6960),createMethod=function(e){return function(t,n,s){var a=o(t),u=i(a);if(0===u)return!e&&-1;var c,l=r(s,u);if(e&&n!=n){for(;u>l;)if((c=a[l++])!=c)return!0}else for(;u>l;l++)if((e||l in a)&&a[l]===n)return e||l||0;return!e&&-1}};e.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},1278:(e,t,n)=>{"use strict";var o=n(4762),r=o({}.toString),i=o("".slice);e.exports=function(e){return i(r(e),8,-1)}},6726:(e,t,n)=>{"use strict";var o=n(5755),r=n(9497),i=n(4961),s=n(5835);e.exports=function(e,t,n){for(var a=r(t),u=s.f,c=i.f,l=0;l<a.length;l++){var d=a[l];o(e,d)||n&&o(n,d)||u(e,d,c(t,d))}}},9037:(e,t,n)=>{"use strict";var o=n(382),r=n(5835),i=n(7738);e.exports=o?function(e,t,n){return r.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},7738:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},7914:(e,t,n)=>{"use strict";var o=n(1483),r=n(5835),i=n(169),s=n(2095);e.exports=function(e,t,n,a){a||(a={});var u=a.enumerable,c=void 0!==a.name?a.name:t;if(o(n)&&i(n,c,a),a.global)u?e[t]=n:s(t,n);else{try{a.unsafe?e[t]&&(u=!0):delete e[t]}catch(e){}u?e[t]=n:r.f(e,t,{value:n,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},2095:(e,t,n)=>{"use strict";var o=n(5578),r=Object.defineProperty;e.exports=function(e,t){try{r(o,e,{value:t,configurable:!0,writable:!0})}catch(n){o[e]=t}return t}},382:(e,t,n)=>{"use strict";var o=n(8473);e.exports=!o((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3145:(e,t,n)=>{"use strict";var o=n(5578),r=n(1704),i=o.document,s=r(i)&&r(i.createElement);e.exports=function(e){return s?i.createElement(e):{}}},4741:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},9461:(e,t,n)=>{"use strict";var o=n(5578).navigator,r=o&&o.userAgent;e.exports=r?String(r):""},6477:(e,t,n)=>{"use strict";var o,r,i=n(5578),s=n(9461),a=i.process,u=i.Deno,c=a&&a.versions||u&&u.version,l=c&&c.v8;l&&(r=(o=l.split("."))[0]>0&&o[0]<4?1:+(o[0]+o[1])),!r&&s&&(!(o=s.match(/Edge\/(\d+)/))||o[1]>=74)&&(o=s.match(/Chrome\/(\d+)/))&&(r=+o[1]),e.exports=r},8612:(e,t,n)=>{"use strict";var o=n(5578),r=n(4961).f,i=n(9037),s=n(7914),a=n(2095),u=n(6726),c=n(8730);e.exports=function(e,t){var n,l,d,p,f,m=e.target,h=e.global,v=e.stat;if(n=h?o:v?o[m]||a(m,{}):o[m]&&o[m].prototype)for(l in t){if(p=t[l],d=e.dontCallGetSet?(f=r(n,l))&&f.value:n[l],!c(h?l:m+(v?".":"#")+l,e.forced)&&void 0!==d){if(typeof p==typeof d)continue;u(p,d)}(e.sham||d&&d.sham)&&i(p,"sham",!0),s(n,l,p,e)}}},8473:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},274:(e,t,n)=>{"use strict";var o=n(8473);e.exports=!o((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},1807:(e,t,n)=>{"use strict";var o=n(274),r=Function.prototype.call;e.exports=o?r.bind(r):function(){return r.apply(r,arguments)}},2048:(e,t,n)=>{"use strict";var o=n(382),r=n(5755),i=Function.prototype,s=o&&Object.getOwnPropertyDescriptor,a=r(i,"name"),u=a&&"something"===function something(){}.name,c=a&&(!o||o&&s(i,"name").configurable);e.exports={EXISTS:a,PROPER:u,CONFIGURABLE:c}},4762:(e,t,n)=>{"use strict";var o=n(274),r=Function.prototype,i=r.call,s=o&&r.bind.bind(i,i);e.exports=o?s:function(e){return function(){return i.apply(e,arguments)}}},1409:(e,t,n)=>{"use strict";var o=n(5578),r=n(1483);e.exports=function(e,t){return arguments.length<2?(n=o[e],r(n)?n:void 0):o[e]&&o[e][t];var n}},2564:(e,t,n)=>{"use strict";var o=n(8120),r=n(5983);e.exports=function(e,t){var n=e[t];return r(n)?void 0:o(n)}},5578:function(e,t,n){"use strict";var check=function(e){return e&&e.Math===Math&&e};e.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof n.g&&n.g)||check("object"==typeof this&&this)||function(){return this}()||Function("return this")()},5755:(e,t,n)=>{"use strict";var o=n(4762),r=n(2347),i=o({}.hasOwnProperty);e.exports=Object.hasOwn||function hasOwn(e,t){return i(r(e),t)}},1507:e=>{"use strict";e.exports={}},2811:(e,t,n)=>{"use strict";var o=n(1409);e.exports=o("document","documentElement")},1799:(e,t,n)=>{"use strict";var o=n(382),r=n(8473),i=n(3145);e.exports=!o&&!r((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},2121:(e,t,n)=>{"use strict";var o=n(4762),r=n(8473),i=n(1278),s=Object,a=o("".split);e.exports=r((function(){return!s("z").propertyIsEnumerable(0)}))?function(e){return"String"===i(e)?a(e,""):s(e)}:s},7268:(e,t,n)=>{"use strict";var o=n(4762),r=n(1483),i=n(1831),s=o(Function.toString);r(i.inspectSource)||(i.inspectSource=function(e){return s(e)}),e.exports=i.inspectSource},4483:(e,t,n)=>{"use strict";var o,r,i,s=n(4644),a=n(5578),u=n(1704),c=n(9037),l=n(5755),d=n(1831),p=n(5409),f=n(1507),m="Object already initialized",h=a.TypeError,v=a.WeakMap;if(s||d.state){var b=d.state||(d.state=new v);b.get=b.get,b.has=b.has,b.set=b.set,o=function(e,t){if(b.has(e))throw new h(m);return t.facade=e,b.set(e,t),t},r=function(e){return b.get(e)||{}},i=function(e){return b.has(e)}}else{var g=p("state");f[g]=!0,o=function(e,t){if(l(e,g))throw new h(m);return t.facade=e,c(e,g,t),t},r=function(e){return l(e,g)?e[g]:{}},i=function(e){return l(e,g)}}e.exports={set:o,get:r,has:i,enforce:function(e){return i(e)?r(e):o(e,{})},getterFor:function(e){return function(t){var n;if(!u(t)||(n=r(t)).type!==e)throw new h("Incompatible receiver, "+e+" required");return n}}}},1483:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},8730:(e,t,n)=>{"use strict";var o=n(8473),r=n(1483),i=/#|\.prototype\./,isForced=function(e,t){var n=a[s(e)];return n===c||n!==u&&(r(t)?o(t):!!t)},s=isForced.normalize=function(e){return String(e).replace(i,".").toLowerCase()},a=isForced.data={},u=isForced.NATIVE="N",c=isForced.POLYFILL="P";e.exports=isForced},5983:e=>{"use strict";e.exports=function(e){return null==e}},1704:(e,t,n)=>{"use strict";var o=n(1483);e.exports=function(e){return"object"==typeof e?null!==e:o(e)}},9557:e=>{"use strict";e.exports=!1},1423:(e,t,n)=>{"use strict";var o=n(1409),r=n(1483),i=n(4815),s=n(5022),a=Object;e.exports=s?function(e){return"symbol"==typeof e}:function(e){var t=o("Symbol");return r(t)&&i(t.prototype,a(e))}},6960:(e,t,n)=>{"use strict";var o=n(8324);e.exports=function(e){return o(e.length)}},169:(e,t,n)=>{"use strict";var o=n(4762),r=n(8473),i=n(1483),s=n(5755),a=n(382),u=n(2048).CONFIGURABLE,c=n(7268),l=n(4483),d=l.enforce,p=l.get,f=String,m=Object.defineProperty,h=o("".slice),v=o("".replace),b=o([].join),g=a&&!r((function(){return 8!==m((function(){}),"length",{value:8}).length})),C=String(String).split("String"),y=e.exports=function(e,t,n){"Symbol("===h(f(t),0,7)&&(t="["+v(f(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!s(e,"name")||u&&e.name!==t)&&(a?m(e,"name",{value:t,configurable:!0}):e.name=t),g&&n&&s(n,"arity")&&e.length!==n.arity&&m(e,"length",{value:n.arity});try{n&&s(n,"constructor")&&n.constructor?a&&m(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var o=d(e);return s(o,"source")||(o.source=b(C,"string"==typeof t?t:"")),e};Function.prototype.toString=y((function toString(){return i(this)&&p(this).source||c(this)}),"toString")},1703:e=>{"use strict";var t=Math.ceil,n=Math.floor;e.exports=Math.trunc||function trunc(e){var o=+e;return(o>0?n:t)(o)}},5290:(e,t,n)=>{"use strict";var o,r=n(2293),i=n(5799),s=n(4741),a=n(1507),u=n(2811),c=n(3145),l=n(5409),d="prototype",p="script",f=l("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(e){return"<"+p+">"+e+"</"+p+">"},NullProtoObjectViaActiveX=function(e){e.write(scriptTag("")),e.close();var t=e.parentWindow.Object;return e=null,t},NullProtoObject=function(){try{o=new ActiveXObject("htmlfile")}catch(e){}var e,t,n;NullProtoObject="undefined"!=typeof document?document.domain&&o?NullProtoObjectViaActiveX(o):(t=c("iframe"),n="java"+p+":",t.style.display="none",u.appendChild(t),t.src=String(n),(e=t.contentWindow.document).open(),e.write(scriptTag("document.F=Object")),e.close(),e.F):NullProtoObjectViaActiveX(o);for(var r=s.length;r--;)delete NullProtoObject[d][s[r]];return NullProtoObject()};a[f]=!0,e.exports=Object.create||function create(e,t){var n;return null!==e?(EmptyConstructor[d]=r(e),n=new EmptyConstructor,EmptyConstructor[d]=null,n[f]=e):n=NullProtoObject(),void 0===t?n:i.f(n,t)}},5799:(e,t,n)=>{"use strict";var o=n(382),r=n(3896),i=n(5835),s=n(2293),a=n(5599),u=n(3658);t.f=o&&!r?Object.defineProperties:function defineProperties(e,t){s(e);for(var n,o=a(t),r=u(t),c=r.length,l=0;c>l;)i.f(e,n=r[l++],o[n]);return e}},5835:(e,t,n)=>{"use strict";var o=n(382),r=n(1799),i=n(3896),s=n(2293),a=n(3815),u=TypeError,c=Object.defineProperty,l=Object.getOwnPropertyDescriptor,d="enumerable",p="configurable",f="writable";t.f=o?i?function defineProperty(e,t,n){if(s(e),t=a(t),s(n),"function"==typeof e&&"prototype"===t&&"value"in n&&f in n&&!n[f]){var o=l(e,t);o&&o[f]&&(e[t]=n.value,n={configurable:p in n?n[p]:o[p],enumerable:d in n?n[d]:o[d],writable:!1})}return c(e,t,n)}:c:function defineProperty(e,t,n){if(s(e),t=a(t),s(n),r)try{return c(e,t,n)}catch(e){}if("get"in n||"set"in n)throw new u("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},4961:(e,t,n)=>{"use strict";var o=n(382),r=n(1807),i=n(7611),s=n(7738),a=n(5599),u=n(3815),c=n(5755),l=n(1799),d=Object.getOwnPropertyDescriptor;t.f=o?d:function getOwnPropertyDescriptor(e,t){if(e=a(e),t=u(t),l)try{return d(e,t)}catch(e){}if(c(e,t))return s(!r(i.f,e,t),e[t])}},2278:(e,t,n)=>{"use strict";var o=n(6742),r=n(4741).concat("length","prototype");t.f=Object.getOwnPropertyNames||function getOwnPropertyNames(e){return o(e,r)}},4347:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},4815:(e,t,n)=>{"use strict";var o=n(4762);e.exports=o({}.isPrototypeOf)},6742:(e,t,n)=>{"use strict";var o=n(4762),r=n(5755),i=n(5599),s=n(6651).indexOf,a=n(1507),u=o([].push);e.exports=function(e,t){var n,o=i(e),c=0,l=[];for(n in o)!r(a,n)&&r(o,n)&&u(l,n);for(;t.length>c;)r(o,n=t[c++])&&(~s(l,n)||u(l,n));return l}},3658:(e,t,n)=>{"use strict";var o=n(6742),r=n(4741);e.exports=Object.keys||function keys(e){return o(e,r)}},7611:(e,t)=>{"use strict";var n={}.propertyIsEnumerable,o=Object.getOwnPropertyDescriptor,r=o&&!n.call({1:2},1);t.f=r?function propertyIsEnumerable(e){var t=o(this,e);return!!t&&t.enumerable}:n},348:(e,t,n)=>{"use strict";var o=n(1807),r=n(1483),i=n(1704),s=TypeError;e.exports=function(e,t){var n,a;if("string"===t&&r(n=e.toString)&&!i(a=o(n,e)))return a;if(r(n=e.valueOf)&&!i(a=o(n,e)))return a;if("string"!==t&&r(n=e.toString)&&!i(a=o(n,e)))return a;throw new s("Can't convert object to primitive value")}},9497:(e,t,n)=>{"use strict";var o=n(1409),r=n(4762),i=n(2278),s=n(4347),a=n(2293),u=r([].concat);e.exports=o("Reflect","ownKeys")||function ownKeys(e){var t=i.f(a(e)),n=s.f;return n?u(t,n(e)):t}},3312:(e,t,n)=>{"use strict";var o=n(5983),r=TypeError;e.exports=function(e){if(o(e))throw new r("Can't call method on "+e);return e}},5409:(e,t,n)=>{"use strict";var o=n(7255),r=n(1866),i=o("keys");e.exports=function(e){return i[e]||(i[e]=r(e))}},1831:(e,t,n)=>{"use strict";var o=n(9557),r=n(5578),i=n(2095),s="__core-js_shared__",a=e.exports=r[s]||i(s,{});(a.versions||(a.versions=[])).push({version:"3.38.1",mode:o?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",source:"https://github.com/zloirock/core-js"})},7255:(e,t,n)=>{"use strict";var o=n(1831);e.exports=function(e,t){return o[e]||(o[e]=t||{})}},6029:(e,t,n)=>{"use strict";var o=n(6477),r=n(8473),i=n(5578).String;e.exports=!!Object.getOwnPropertySymbols&&!r((function(){var e=Symbol("symbol detection");return!i(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&o&&o<41}))},3392:(e,t,n)=>{"use strict";var o=n(3005),r=Math.max,i=Math.min;e.exports=function(e,t){var n=o(e);return n<0?r(n+t,0):i(n,t)}},5599:(e,t,n)=>{"use strict";var o=n(2121),r=n(3312);e.exports=function(e){return o(r(e))}},3005:(e,t,n)=>{"use strict";var o=n(1703);e.exports=function(e){var t=+e;return t!=t||0===t?0:o(t)}},8324:(e,t,n)=>{"use strict";var o=n(3005),r=Math.min;e.exports=function(e){var t=o(e);return t>0?r(t,9007199254740991):0}},2347:(e,t,n)=>{"use strict";var o=n(3312),r=Object;e.exports=function(e){return r(o(e))}},2355:(e,t,n)=>{"use strict";var o=n(1807),r=n(1704),i=n(1423),s=n(2564),a=n(348),u=n(1),c=TypeError,l=u("toPrimitive");e.exports=function(e,t){if(!r(e)||i(e))return e;var n,u=s(e,l);if(u){if(void 0===t&&(t="default"),n=o(u,e,t),!r(n)||i(n))return n;throw new c("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(e,t)}},3815:(e,t,n)=>{"use strict";var o=n(2355),r=n(1423);e.exports=function(e){var t=o(e,"string");return r(t)?t:t+""}},8761:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},1866:(e,t,n)=>{"use strict";var o=n(4762),r=0,i=Math.random(),s=o(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+s(++r+i,36)}},5022:(e,t,n)=>{"use strict";var o=n(6029);e.exports=o&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},3896:(e,t,n)=>{"use strict";var o=n(382),r=n(8473);e.exports=o&&r((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},4644:(e,t,n)=>{"use strict";var o=n(5578),r=n(1483),i=o.WeakMap;e.exports=r(i)&&/native code/.test(String(i))},1:(e,t,n)=>{"use strict";var o=n(5578),r=n(7255),i=n(5755),s=n(1866),a=n(6029),u=n(5022),c=o.Symbol,l=r("wks"),d=u?c.for||c:c&&c.withoutSetter||s;e.exports=function(e){return i(l,e)||(l[e]=a&&i(c,e)?c[e]:d("Symbol."+e)),l[e]}},6281:(e,t,n)=>{"use strict";var o=n(8612),r=n(6651).includes,i=n(8473),s=n(7095);o({target:"Array",proto:!0,forced:i((function(){return!Array(1).includes()}))},{includes:function includes(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}}),s("includes")}},t={};function __webpack_require__(n){var o=t[n];if(void 0!==o)return o.exports;var r=t[n]={exports:{}};return e[n].call(r.exports,r,r.exports,__webpack_require__),r.exports}__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();(()=>{"use strict";var e=__webpack_require__(2470).__,t=__webpack_require__(6784);var n=t(__webpack_require__(1594)),o=t(__webpack_require__(2239)),r=t(__webpack_require__(9212)),i=t(__webpack_require__(1755));class CustomCode extends elementorModules.Module{constructor(){super(),jQuery(this.initialize.bind(this))}initialize(){$e.components.register(new o.default),ReactDOM.render(n.default.createElement(r.default,null),document.querySelector(".post-conditions")),this.addTipsyToFields(),this.addDescription(),this.addLocationChangeHandler(),this.addOpenAIButton(),this.setOptionsPlacementVisibility("elementor_body_end"===jQuery("#location").val())}addTipsyToFields(){jQuery(".elementor-field-label i[data-info]").tipsy({title(){return this.getAttribute("data-info")},gravity:()=>"s"})}addDescription(){const t="<p>"+e("Manage and create all of your custom code here.<br />Organize all of your custom code and incorporate code snippets in your site. Add tracking codes, meta titles, and other scripts. Set display conditions, locations, and priority all from one place.","elementor-pro")+'&nbsp;<a target="_blank" href="https://go.elementor.com/wp-dash-custom-code">'+e("Learn more","elementor-pro")+"</a></p>";jQuery(t).insertBefore(".wp-header-end")}addLocationChangeHandler(){jQuery("#location").on("change",(e=>{this.setOptionsPlacementVisibility("elementor_body_end"===e.target.value)}))}addOpenAIButton(){const t=jQuery(`<button class="e-ai-button"><i class="eicon-ai"></i> ${e("Code with AI","elementor-pro")}</button>`);t.on("click",(e=>{e.preventDefault();const t=elementorCommon.config.isRTL,o=document.createElement("div");document.body.append(o),ReactDOM.render(n.default.createElement(i.default,{type:"code",getControlValue:()=>document.querySelector(".CodeMirror").CodeMirror.getValue(),setControlValue:e=>document.querySelector(".CodeMirror").CodeMirror.setValue(e),additionalOptions:{codeLanguage:"html"},onClose:()=>{ReactDOM.unmountComponentAtNode(o),o.parentNode.removeChild(o)},isRTL:t}),o)})),jQuery(".elementor-field.location.elementor-field-select").after(t)}setOptionsPlacementVisibility(e){jQuery(".elementor-custom-code-options-placement").toggleClass("show",e)}}elementorProAdmin.customCode=new CustomCode})()})();