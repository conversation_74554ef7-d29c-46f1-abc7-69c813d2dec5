/*! elementor-pro - v3.29.0 - 19-05-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[416],{475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=elementorModules.frontend.handlers.Base.extend({cache:null,cacheElements(){const e=this.$element.find(".elementor-countdown-wrapper");this.cache={$countDown:e,timeInterval:null,elements:{$countdown:e.find(".elementor-countdown-wrapper"),$daysSpan:e.find(".elementor-countdown-days"),$hoursSpan:e.find(".elementor-countdown-hours"),$minutesSpan:e.find(".elementor-countdown-minutes"),$secondsSpan:e.find(".elementor-countdown-seconds"),$expireMessage:e.parent().find(".elementor-countdown-expire--message")},data:{id:this.$element.data("id"),endTime:new Date(1e3*e.data("date")),actions:e.data("expire-actions"),evergreenInterval:e.data("evergreen-interval")}}},onInit(){elementorModules.frontend.handlers.Base.prototype.onInit.apply(this,arguments),this.cacheElements(),0<this.cache.data.evergreenInterval&&(this.cache.data.endTime=this.getEvergreenDate()),this.initializeClock()},updateClock(){const e=this,t=this.getTimeRemaining(this.cache.data.endTime);jQuery.each(t.parts,(function(t){const n=e.cache.elements["$"+t+"Span"];let a=this.toString();1===a.length&&(a=0+a),n.length&&n.text(a)})),t.total<=0&&(clearInterval(this.cache.timeInterval),this.runActions())},initializeClock(){const e=this;this.updateClock(),this.cache.timeInterval=setInterval((function(){e.updateClock()}),1e3)},runActions(){const e=this;e.$element.trigger("countdown_expire",e.$element),this.cache.data.actions&&this.cache.data.actions.forEach((function(t){switch(t.type){case"hide":e.cache.$countDown.hide();break;case"redirect":t.redirect_url&&t.redirect_url.startsWith("http")&&(window.location.href=t.redirect_url);break;case"message":e.cache.elements.$expireMessage.show()}}))},getTimeRemaining(e){const t=e-new Date;let n=Math.floor(t/1e3%60),a=Math.floor(t/1e3/60%60),r=Math.floor(t/36e5%24),o=Math.floor(t/864e5);return(o<0||r<0||a<0)&&(n=a=r=o=0),{total:t,parts:{days:o,hours:r,minutes:a,seconds:n}}},getEvergreenDate(){const e=this,t=this.cache.data.id,n=this.cache.data.evergreenInterval,a=t+"-evergreen_due_date",r=t+"-evergreen_interval",o={dueDate:localStorage.getItem(a),interval:localStorage.getItem(r)},initEvergreen=function(){var t=new Date;return e.cache.data.endTime=t.setSeconds(t.getSeconds()+n),localStorage.setItem(a,e.cache.data.endTime),localStorage.setItem(r,n),e.cache.data.endTime};return null===o.dueDate&&null===o.interval||null!==o.dueDate&&n!==parseInt(o.interval,10)?initEvergreen():o.dueDate>0&&parseInt(o.interval,10)===n?o.dueDate:void 0}})}}]);