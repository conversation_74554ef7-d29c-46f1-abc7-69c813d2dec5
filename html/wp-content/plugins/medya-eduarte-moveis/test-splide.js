/* Teste do Splide - MEDYA Eduarte Móveis */

(function() {
    console.log('🎯 TESTE DO SPLIDE INICIADO');
    
    function testSplideSlideshow() {
        const slideshows = document.querySelectorAll('.medya-eduarte-slideshow');
        console.log('📊 Slideshows encontrados:', slideshows.length);
        
        if (slideshows.length === 0) {
            console.log('❌ Nenhum slideshow encontrado!');
            return;
        }
        
        slideshows.forEach((slideshow, index) => {
            console.log(`\n🎯 Testando slideshow ${index + 1}:`);
            
            // Verifica se é Splide
            const isSplide = slideshow.classList.contains('splide');
            console.log('📱 É Splide:', isSplide ? '✅ Sim' : '❌ Não');
            
            // Verifica instância Splide
            const splide = slideshow.splide || jQuery(slideshow).data('splide');
            console.log('🔧 Instância Splide:', splide ? '✅ Encontrada' : '❌ Não encontrada');
            
            if (!splide) {
                console.log('⚠️ Splide não inicializado');
                return;
            }
            
            // Informações básicas
            console.log('📋 Informações básicas:');
            console.log('  - Slides total:', splide.length);
            console.log('  - Slide ativo:', splide.index);
            console.log('  - Tipo:', splide.options.type);
            console.log('  - Autoplay:', splide.options.autoplay);
            console.log('  - Intervalo:', splide.options.interval);
            
            // Verifica slides visíveis
            const slides = slideshow.querySelectorAll('.splide__slide');
            const activeSlides = slideshow.querySelectorAll('.splide__slide.is-active');
            const visibleSlides = Array.from(slides).filter(slide => {
                const style = window.getComputedStyle(slide);
                return style.opacity > 0 && style.visibility !== 'hidden';
            });
            
            console.log('👁️ Visibilidade:');
            console.log('  - Total slides:', slides.length);
            console.log('  - Slides ativos:', activeSlides.length);
            console.log('  - Slides visíveis:', visibleSlides.length);
            
            if (visibleSlides.length > 0) {
                console.log('✅ Slides visíveis encontrados!');
            } else {
                console.log('❌ Nenhum slide visível!');
            }
            
            // Testa mudança manual
            console.log('🧪 Testando mudança manual...');
            const originalIndex = splide.index;
            
            setTimeout(() => {
                splide.go('+1');
                console.log(`📍 Tentou mudar do slide ${originalIndex} para o próximo`);
                
                setTimeout(() => {
                    const newIndex = splide.index;
                    if (newIndex !== originalIndex) {
                        console.log(`✅ Mudança manual funcionou: ${originalIndex} → ${newIndex}`);
                    } else {
                        console.log('❌ Mudança manual falhou');
                    }
                }, 200);
            }, 1000);
            
            // Testa autoplay
            if (splide.options.autoplay) {
                console.log('🔄 Testando autoplay...');
                
                setTimeout(() => {
                    const isPlaying = splide.Components.Autoplay.isPaused();
                    console.log('📊 Autoplay status:', isPlaying ? '⏸️ Pausado' : '▶️ Rodando');
                    
                    if (isPlaying) {
                        splide.Components.Autoplay.play();
                        console.log('🔄 Autoplay reiniciado');
                    }
                }, 2000);
            }
        });
    }
    
    function monitorSplideSlideshow() {
        console.log('👁️ Iniciando monitoramento do Splide...');
        
        let monitorCount = 0;
        const monitorInterval = setInterval(() => {
            monitorCount++;
            
            const slideshows = document.querySelectorAll('.medya-eduarte-slideshow.splide');
            
            slideshows.forEach((slideshow, index) => {
                const splide = slideshow.splide || jQuery(slideshow).data('splide');
                if (splide) {
                    const isAutoplayRunning = splide.options.autoplay && !splide.Components.Autoplay.isPaused();
                    console.log(`📊 Monitor ${monitorCount} - Slideshow ${index + 1}: Slide ${splide.index}, Autoplay: ${isAutoplayRunning ? '✅' : '❌'}`);
                }
            });
            
            if (monitorCount >= 10) { // 50 segundos
                clearInterval(monitorInterval);
                console.log('🏁 Monitor finalizado');
            }
        }, 5000);
    }
    
    function forceSplideVisibility() {
        console.log('🔧 Forçando visibilidade dos slides Splide...');
        
        const slideshows = document.querySelectorAll('.medya-eduarte-slideshow.splide');
        
        slideshows.forEach((slideshow, index) => {
            console.log(`🎯 Corrigindo slideshow ${index + 1}:`);
            
            const slides = slideshow.querySelectorAll('.splide__slide');
            const activeSlides = slideshow.querySelectorAll('.splide__slide.is-active');
            
            // Garante que slides sejam visíveis
            slides.forEach(slide => {
                slide.style.opacity = '1';
                slide.style.visibility = 'visible';
                slide.style.display = 'block';
            });
            
            // Se for fade, esconde slides não ativos
            if (slideshow.classList.contains('splide--fade')) {
                slides.forEach(slide => {
                    if (!slide.classList.contains('is-active')) {
                        slide.style.opacity = '0';
                    }
                });
            }
            
            console.log(`✅ Slideshow ${index + 1} corrigido - ${slides.length} slides, ${activeSlides.length} ativos`);
        });
    }
    
    function debugSplideStructure() {
        console.log('🔍 DEBUG DA ESTRUTURA SPLIDE:');
        
        const slideshows = document.querySelectorAll('.medya-eduarte-slideshow');
        
        slideshows.forEach((slideshow, index) => {
            console.log(`\n📋 Slideshow ${index + 1}:`);
            console.log('Classes:', slideshow.className);
            
            const track = slideshow.querySelector('.splide__track');
            const list = slideshow.querySelector('.splide__list');
            const slides = slideshow.querySelectorAll('.splide__slide');
            
            console.log('Estrutura:');
            console.log('  - Track:', track ? '✅' : '❌');
            console.log('  - List:', list ? '✅' : '❌');
            console.log('  - Slides:', slides.length);
            
            if (slides.length > 0) {
                console.log('Slides:');
                slides.forEach((slide, i) => {
                    const isActive = slide.classList.contains('is-active');
                    const opacity = window.getComputedStyle(slide).opacity;
                    console.log(`  - Slide ${i + 1}: ${isActive ? '🟢 Ativo' : '⚪ Inativo'}, Opacity: ${opacity}`);
                });
            }
        });
    }
    
    // Executa teste inicial
    testSplideSlideshow();
    
    // Executa debug da estrutura
    setTimeout(debugSplideStructure, 2000);
    
    // Expõe funções globalmente
    window.testSplideSlideshow = testSplideSlideshow;
    window.monitorSplideSlideshow = monitorSplideSlideshow;
    window.forceSplideVisibility = forceSplideVisibility;
    window.debugSplideStructure = debugSplideStructure;
    
    console.log('✅ TESTE DO SPLIDE CONFIGURADO');
    console.log('🛠️ Funções disponíveis:');
    console.log('  - testSplideSlideshow() - Teste completo');
    console.log('  - monitorSplideSlideshow() - Monitor contínuo');
    console.log('  - forceSplideVisibility() - Força visibilidade');
    console.log('  - debugSplideStructure() - Debug da estrutura');
    
})();

// Para usar:
// 1. Cole este código no console
// 2. Execute testSplideSlideshow() para teste completo
// 3. Execute forceSplideVisibility() se slides não aparecerem
// 4. Execute monitorSplideSlideshow() para monitoramento contínuo
