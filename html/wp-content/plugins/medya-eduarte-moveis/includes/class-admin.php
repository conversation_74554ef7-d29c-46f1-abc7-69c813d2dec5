<?php
if (!defined('ABSPATH')) {
    exit;
}

class Medya_Eduarte_Moveis_Admin
{
    private $version;
    private $option_sections;
    private $option_post_type;
    private $menu_slug;

    public function __construct($version, $option_sections, $option_post_type, $menu_slug)
    {
        $this->version = $version;
        $this->option_sections = $option_sections;
        $this->option_post_type = $option_post_type;
        $this->menu_slug = $menu_slug;

        add_action('admin_menu', [$this, 'add_settings_submenu']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_init', [$this, 'handle_form_submission']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
        add_action('wp_ajax_medya_eduarte_save_sections', [$this, 'ajax_save_sections']);
        add_action('wp_ajax_medya_eduarte_get_posts', [$this, 'ajax_get_posts']);
    }

    public function add_settings_submenu()
    {
        add_submenu_page(
            'medya-main-menu',
            esc_html__('Eduarte Móveis', 'medya-eduarte-moveis'),
            esc_html__('Eduarte Móveis', 'medya-eduarte-moveis'),
            'manage_options',
            $this->menu_slug,
            [$this, 'render_settings_page']
        );
    }

    public function register_settings()
    {
        register_setting('medya_eduarte_group', $this->option_sections, [
            'sanitize_callback' => [$this, 'sanitize_sections']
        ]);
        register_setting('medya_eduarte_group', $this->option_post_type);
    }

    public function handle_form_submission()
    {
        // Verifica se é nossa página e se há dados sendo submetidos
        if (!isset($_POST['option_page']) || $_POST['option_page'] !== 'medya_eduarte_group') {
            return;
        }

        // Verifica nonce
        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'medya_eduarte_group-options')) {
            return;
        }

        // Verifica permissões
        if (!current_user_can('manage_options')) {
            return;
        }

        // Processa seções se existirem
        if (isset($_POST[$this->option_sections])) {
            $sections_data = $_POST[$this->option_sections];
            $sections = json_decode(stripslashes($sections_data), true);

            if (is_array($sections)) {
                update_option($this->option_sections, $sections);

                // Adiciona notice de sucesso
                add_action('admin_notices', function () {
                    echo '<div class="notice notice-success is-dismissible"><p>';
                    echo esc_html__('Configurações salvas com sucesso!', 'medya-eduarte-moveis');
                    echo '</p></div>';
                });
            } else {
                // Adiciona notice de erro
                add_action('admin_notices', function () {
                    echo '<div class="notice notice-error is-dismissible"><p>';
                    echo esc_html__('Erro ao salvar as seções. Verifique os dados e tente novamente.', 'medya-eduarte-moveis');
                    echo '</p></div>';
                });
            }
        }
    }

    public function sanitize_sections($input)
    {
        if (is_string($input)) {
            $sections = json_decode(stripslashes($input), true);
            if (is_array($sections)) {
                return $sections;
            }
        }

        if (is_array($input)) {
            return $input;
        }

        return [];
    }

    public function enqueue_admin_scripts($hook)
    {
        if (strpos($hook, $this->menu_slug) === false) {
            return;
        }

        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_script(
            'medya-eduarte-admin',
            MEDYA_EDUARTE_URL . 'assets/admin.js',
            ['jquery', 'jquery-ui-sortable'],
            $this->version,
            true
        );

        wp_localize_script('medya-eduarte-admin', 'medyaEduarte', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('medya_eduarte_nonce'),
            'strings' => [
                'confirmDelete' => esc_html__('Tem certeza que deseja excluir esta seção?', 'medya-eduarte-moveis'),
                'sectionName' => esc_html__('Nome da seção', 'medya-eduarte-moveis'),
                'selectPost' => esc_html__('Selecione um post', 'medya-eduarte-moveis'),
                'loading' => esc_html__('Carregando...', 'medya-eduarte-moveis'),
                'error' => esc_html__('Erro ao carregar posts', 'medya-eduarte-moveis')
            ]
        ]);

        wp_enqueue_style(
            'medya-eduarte-admin',
            MEDYA_EDUARTE_URL . 'assets/admin.css',
            [],
            $this->version
        );
    }

    public function render_settings_page()
    {
        $sections = get_option($this->option_sections, []);
        // Garante que $sections é sempre um array
        if (!is_array($sections)) {
            $sections = [];
        }

        $selected_post_type = get_option($this->option_post_type, 'post');
        $post_types = get_post_types(['public' => true], 'objects');
?>
        <div class="wrap">
            <h1><?php esc_html_e('Eduarte Móveis - Configurações', 'medya-eduarte-moveis'); ?></h1>

            <form method="post" action="options.php">
                <?php settings_fields('medya_eduarte_group'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="post_type_select"><?php esc_html_e('Tipo de Post para Galerias', 'medya-eduarte-moveis'); ?></label>
                        </th>
                        <td>
                            <select name="<?php echo esc_attr($this->option_post_type); ?>" id="post_type_select">
                                <?php foreach ($post_types as $post_type): ?>
                                    <option value="<?php echo esc_attr($post_type->name); ?>" <?php selected($selected_post_type, $post_type->name); ?>>
                                        <?php echo esc_html($post_type->label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description">
                                <?php esc_html_e('Selecione o tipo de post que contém as galerias de imagens.', 'medya-eduarte-moveis'); ?>
                            </p>
                        </td>
                    </tr>
                </table>

                <h2><?php esc_html_e('Seções de Galeria da Home', 'medya-eduarte-moveis'); ?></h2>

                <div id="sections-container">
                    <div id="sections-list">
                        <?php $this->render_sections_list($sections, $selected_post_type); ?>
                    </div>

                    <div class="section-add-container">
                        <input type="text" id="new-section-name" placeholder="<?php esc_attr_e('Nome da nova seção', 'medya-eduarte-moveis'); ?>" />
                        <button type="button" id="add-section-btn" class="button">
                            <?php esc_html_e('Adicionar Seção', 'medya-eduarte-moveis'); ?>
                        </button>
                    </div>
                </div>

                <input type="hidden" name="<?php echo esc_attr($this->option_sections); ?>" id="sections-data" value="<?php echo esc_attr(json_encode($sections)); ?>" />

                <?php submit_button(); ?>
            </form>
        </div>
    <?php
    }

    private function render_sections_list($sections, $post_type)
    {
        // Garante que $sections é um array
        if (!is_array($sections)) {
            $sections = [];
        }

        if (empty($sections)) {
            echo '<p class="no-sections">' . esc_html__('Nenhuma seção configurada ainda.', 'medya-eduarte-moveis') . '</p>';
            return;
        }

        // Ordena por order
        usort($sections, function ($a, $b) {
            return ($a['order'] ?? 0) - ($b['order'] ?? 0);
        });

        echo '<div class="sections-sortable">';
        foreach ($sections as $section) {
            $this->render_section_item($section, $post_type);
        }
        echo '</div>';
    }

    private function render_section_item($section, $post_type)
    {
        $posts = get_posts([
            'post_type' => $post_type,
            'numberposts' => -1,
            'post_status' => 'publish'
        ]);
    ?>
        <div class="section-item" data-section-id="<?php echo esc_attr($section['id']); ?>">
            <div class="section-header">
                <span class="section-drag-handle">⋮⋮</span>
                <span class="section-name"><?php echo esc_html($section['name']); ?></span>
                <button type="button" class="section-delete-btn" title="<?php esc_attr_e('Excluir seção', 'medya-eduarte-moveis'); ?>">×</button>
            </div>
            <div class="section-content">
                <label><?php esc_html_e('Post vinculado:', 'medya-eduarte-moveis'); ?></label>
                <select class="section-post-select" data-section-id="<?php echo esc_attr($section['id']); ?>">
                    <option value=""><?php esc_html_e('Selecione um post', 'medya-eduarte-moveis'); ?></option>
                    <?php foreach ($posts as $post): ?>
                        <option value="<?php echo esc_attr($post->ID); ?>" <?php selected($section['post_id'] ?? '', $post->ID); ?>>
                            <?php echo esc_html($post->post_title); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
<?php
    }

    public function ajax_save_sections()
    {
        check_ajax_referer('medya_eduarte_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die();
        }

        $sections = json_decode(stripslashes($_POST['sections']), true);

        if (is_array($sections)) {
            update_option($this->option_sections, $sections);
            do_action('medya_eduarte_sections_saved', $sections);
            wp_send_json_success();
        } else {
            wp_send_json_error();
        }
    }

    public function ajax_get_posts()
    {
        check_ajax_referer('medya_eduarte_nonce', 'nonce');

        $post_type = sanitize_text_field($_POST['post_type']);

        $posts = get_posts([
            'post_type' => $post_type,
            'numberposts' => -1,
            'post_status' => 'publish'
        ]);

        $options = [];
        foreach ($posts as $post) {
            $options[] = [
                'value' => $post->ID,
                'label' => $post->post_title
            ];
        }

        wp_send_json_success($options);
    }
}
