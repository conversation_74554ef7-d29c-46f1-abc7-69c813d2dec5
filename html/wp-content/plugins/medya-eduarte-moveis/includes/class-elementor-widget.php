<?php
if (!defined('ABSPATH')) {
    exit;
}

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;

class Medya_Eduarte_Moveis_Elementor_Widget extends Widget_Base
{
    public function get_name()
    {
        return 'medya-eduarte-slideshow';
    }

    public function get_title()
    {
        return esc_html__('Eduarte Slideshow', 'medya-eduarte-moveis');
    }

    public function get_icon()
    {
        return 'eicon-slideshow';
    }

    public function get_categories()
    {
        return ['general'];
    }

    public function get_keywords()
    {
        return ['slideshow', 'gallery', 'eduarte', 'medya'];
    }

    public function get_script_depends()
    {
        return ['medya-eduarte-slideshow'];
    }

    public function get_style_depends()
    {
        return ['medya-eduarte-slideshow'];
    }

    protected function register_controls()
    {
        // Seção de Conteúdo
        $this->start_controls_section(
            'content_section',
            [
                'label' => esc_html__('Conteúdo', 'medya-eduarte-moveis'),
                'tab' => Controls_Manager::TAB_CONTENT,
            ]
        );

        $sections_options = Medya_Eduarte_Moveis::get_sections_options();

        $this->add_control(
            'selected_section',
            [
                'label' => esc_html__('Seção da Galeria', 'medya-eduarte-moveis'),
                'type' => Controls_Manager::SELECT,
                'options' => $sections_options,
                'default' => '',
                'description' => esc_html__('Selecione a seção configurada no painel administrativo.', 'medya-eduarte-moveis'),
            ]
        );

        // Informações úteis no editor
        if (\Elementor\Plugin::$instance->editor->is_edit_mode()) {
            $this->add_control(
                'editor_info',
                [
                    'type' => Controls_Manager::RAW_HTML,
                    'raw' => '<div style="padding: 10px; background: #e3f2fd; border-radius: 4px; margin: 10px 0;">
                        <strong>ℹ️ Informações:</strong><br>
                        • <a href="' . admin_url('admin.php?page=medya-eduarte-moveis') . '" target="_blank">Gerenciar Seções</a><br>
                        • Certifique-se de que o post tem o campo "galeria" preenchido
                    </div>',
                    'content_classes' => 'elementor-panel-alert elementor-panel-alert-info',
                ]
            );
        }

        $this->end_controls_section();

        // Seção de Layout
        $this->start_controls_section(
            'layout_section',
            [
                'label' => esc_html__('Layout', 'medya-eduarte-moveis'),
                'tab' => Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_responsive_control(
            'height',
            [
                'label' => esc_html__('Altura', 'medya-eduarte-moveis'),
                'type' => Controls_Manager::SLIDER,
                'size_units' => ['px', 'vh', '%'],
                'range' => [
                    'px' => [
                        'min' => 100,
                        'max' => 1000,
                    ],
                    'vh' => [
                        'min' => 10,
                        'max' => 100,
                    ],
                    '%' => [
                        'min' => 10,
                        'max' => 100,
                    ],
                ],
                'default' => [
                    'unit' => 'vh',
                    'size' => 50,
                ],
                'selectors' => [
                    '{{WRAPPER}} .medya-eduarte-slideshow' => 'height: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Seção de Configurações do Slideshow
        $this->start_controls_section(
            'slideshow_settings',
            [
                'label' => esc_html__('Configurações do Slideshow', 'medya-eduarte-moveis'),
                'tab' => Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'slideshow_loop',
            [
                'label' => esc_html__('Loop Infinito', 'medya-eduarte-moveis'),
                'type' => Controls_Manager::SWITCHER,
                'default' => 'yes',
                'frontend_available' => true,
            ]
        );

        $this->add_control(
            'slideshow_autoplay',
            [
                'label' => esc_html__('Reprodução Automática', 'medya-eduarte-moveis'),
                'type' => Controls_Manager::SWITCHER,
                'default' => 'yes',
                'frontend_available' => true,
            ]
        );

        $this->add_control(
            'slide_duration',
            [
                'label' => esc_html__('Duração do Slide', 'medya-eduarte-moveis') . ' (ms)',
                'type' => Controls_Manager::NUMBER,
                'default' => 5000,
                'min' => 1000,
                'max' => 20000,
                'step' => 500,
                'condition' => [
                    'slideshow_autoplay' => 'yes',
                ],
                'frontend_available' => true,
            ]
        );

        $this->add_control(
            'slide_transition',
            [
                'label' => esc_html__('Transição', 'medya-eduarte-moveis'),
                'type' => Controls_Manager::SELECT,
                'default' => 'fade',
                'options' => [
                    'fade' => esc_html__('Fade', 'medya-eduarte-moveis'),
                    'slide' => esc_html__('Slide', 'medya-eduarte-moveis'),
                    'cube' => esc_html__('Cube', 'medya-eduarte-moveis'),
                    'coverflow' => esc_html__('Coverflow', 'medya-eduarte-moveis'),
                ],
                'frontend_available' => true,
            ]
        );

        $this->add_control(
            'transition_duration',
            [
                'label' => esc_html__('Duração da Transição', 'medya-eduarte-moveis') . ' (ms)',
                'type' => Controls_Manager::NUMBER,
                'default' => 500,
                'min' => 100,
                'max' => 3000,
                'step' => 100,
                'frontend_available' => true,
            ]
        );

        $this->add_control(
            'background_size',
            [
                'label' => esc_html__('Tamanho do Fundo', 'medya-eduarte-moveis'),
                'type' => Controls_Manager::SELECT,
                'default' => 'cover',
                'options' => [
                    'auto' => esc_html__('Auto', 'medya-eduarte-moveis'),
                    'cover' => esc_html__('Cover', 'medya-eduarte-moveis'),
                    'contain' => esc_html__('Contain', 'medya-eduarte-moveis'),
                ],
                'selectors' => [
                    '{{WRAPPER}} .medya-eduarte-slide' => 'background-size: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'background_position',
            [
                'label' => esc_html__('Posição do Fundo', 'medya-eduarte-moveis'),
                'type' => Controls_Manager::SELECT,
                'default' => 'center center',
                'options' => [
                    'center center' => esc_html__('Center Center', 'medya-eduarte-moveis'),
                    'center left' => esc_html__('Center Left', 'medya-eduarte-moveis'),
                    'center right' => esc_html__('Center Right', 'medya-eduarte-moveis'),
                    'top center' => esc_html__('Top Center', 'medya-eduarte-moveis'),
                    'top left' => esc_html__('Top Left', 'medya-eduarte-moveis'),
                    'top right' => esc_html__('Top Right', 'medya-eduarte-moveis'),
                    'bottom center' => esc_html__('Bottom Center', 'medya-eduarte-moveis'),
                    'bottom left' => esc_html__('Bottom Left', 'medya-eduarte-moveis'),
                    'bottom right' => esc_html__('Bottom Right', 'medya-eduarte-moveis'),
                ],
                'selectors' => [
                    '{{WRAPPER}} .medya-eduarte-slide' => 'background-position: {{VALUE}};',
                ],
            ]
        );



        $this->end_controls_section();

        // Seção de Estilo
        $this->start_controls_section(
            'style_section',
            [
                'label' => esc_html__('Estilo', 'medya-eduarte-moveis'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name' => 'border',
                'selector' => '{{WRAPPER}} .medya-eduarte-slideshow',
            ]
        );

        $this->add_responsive_control(
            'border_radius',
            [
                'label' => esc_html__('Border Radius', 'medya-eduarte-moveis'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .medya-eduarte-slideshow' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'box_shadow',
                'selector' => '{{WRAPPER}} .medya-eduarte-slideshow',
            ]
        );

        $this->end_controls_section();
    }

    protected function render()
    {
        $settings = $this->get_settings_for_display();
        $selected_section = $settings['selected_section'];

        if (empty($selected_section)) {
            if (\Elementor\Plugin::$instance->editor->is_edit_mode()) {
                echo '<div style="padding: 20px; text-align: center; background: #f1f1f1; border: 2px dashed #ccc;">';
                echo esc_html__('Selecione uma seção para exibir o slideshow.', 'medya-eduarte-moveis');
                echo '</div>';
            }
            return;
        }

        $images = Medya_Eduarte_Moveis::get_section_images($selected_section);

        if (empty($images)) {
            if (\Elementor\Plugin::$instance->editor->is_edit_mode()) {
                echo '<div style="padding: 20px; text-align: center; background: #f1f1f1; border: 2px dashed #ccc;">';
                echo esc_html__('Nenhuma imagem encontrada para esta seção.', 'medya-eduarte-moveis');
                echo '<br><small>' . esc_html__('Verifique se o post tem o campo "galeria" preenchido.', 'medya-eduarte-moveis') . '</small>';
                echo '</div>';
            }
            return;
        }

        // Configurações do slideshow
        $slideshow_settings = [
            'loop' => $settings['slideshow_loop'] === 'yes',
            'autoplay' => $settings['slideshow_autoplay'] === 'yes',
            'duration' => $settings['slide_duration'] ?? 5000,
            'effect' => $settings['slide_transition'] ?? 'fade',
            'speed' => $settings['transition_duration'] ?? 500,
        ];

        $this->add_render_attribute('slideshow', [
            'class' => 'medya-eduarte-slideshow splide',
            'data-settings' => json_encode($slideshow_settings),
            'aria-label' => esc_attr__('Galeria de Imagens', 'medya-eduarte-moveis')
        ]);

        // Hook antes de renderizar
        do_action('medya_eduarte_before_slideshow', $selected_section, $images);

?>
        <section <?php $this->print_render_attribute_string('slideshow'); ?>>
            <div class="splide__track">
                <ul class="splide__list">
                    <?php foreach ($images as $image): ?>
                        <li class="medya-eduarte-slide splide__slide"
                            style="background-image: url('<?php echo esc_url($image['url']); ?>');"
                            data-image-id="<?php echo esc_attr($image['id']); ?>"
                            <?php if (!empty($image['alt'])): ?>
                            aria-label="<?php echo esc_attr($image['alt']); ?>"
                            <?php endif; ?>>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </section>
<?php

        // Hook após renderizar
        do_action('medya_eduarte_after_slideshow', $selected_section, $images);
    }
}
