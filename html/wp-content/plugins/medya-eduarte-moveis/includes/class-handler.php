<?php
if (!defined('ABSPATH')) {
    exit;
}

class Medya_Eduarte_Moveis_Handler
{
    private $version;

    public function __construct($version)
    {
        $this->version = $version;

        // Registra o widget do Elementor
        add_action('elementor/widgets/register', [$this, 'register_elementor_widget']);

        // Enfileira assets do frontend
        add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_assets']);
    }

    public function register_elementor_widget($widgets_manager)
    {
        require_once MEDYA_EDUARTE_PATH . 'includes/class-elementor-widget.php';
        $widgets_manager->register(new \Medya_Eduarte_Moveis_Elementor_Widget());
    }

    public function enqueue_frontend_assets()
    {
        // Registra o CSS do slideshow (refatorado)
        wp_register_style(
            'medya-eduarte-slideshow',
            MEDYA_EDUARTE_URL . 'assets/slideshow-new.css',
            [],
            $this->version
        );

        // Registra o JS do slideshow (refatorado)
        wp_register_script(
            'medya-eduarte-slideshow',
            MEDYA_EDUARTE_URL . 'assets/slideshow-new.js',
            ['swiper', 'elementor-frontend'],
            $this->version,
            true
        );
    }
}
