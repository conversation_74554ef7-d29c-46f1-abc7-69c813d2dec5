<?php
if (!defined('ABSPATH')) {
    exit;
}

class Medya_Eduarte_Moveis_Handler
{
    private $version;

    public function __construct($version)
    {
        $this->version = $version;

        // Registra o widget do Elementor
        add_action('elementor/widgets/register', [$this, 'register_elementor_widget']);

        // Registra assets (funciona em frontend e editor)
        add_action('init', [$this, 'register_assets']);

        // Enfileira assets do frontend
        add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_assets']);

        // Enfileira assets no editor do Elementor
        add_action('elementor/editor/before_enqueue_scripts', [$this, 'enqueue_editor_assets']);
        add_action('elementor/preview/enqueue_styles', [$this, 'enqueue_editor_assets']);
    }

    public function register_elementor_widget($widgets_manager)
    {
        require_once MEDYA_EDUARTE_PATH . 'includes/class-elementor-widget.php';
        $widgets_manager->register(new \Medya_Eduarte_Moveis_Elementor_Widget());
    }

    public function register_assets()
    {
        // Registra o CSS do Splide
        wp_register_style(
            'splide_css',
            MEDYA_EDUARTE_URL . 'assets/libs/splide/splide.min.css',
            [],
            '4.1.4'
        );

        // Registra o JS do Splide
        wp_register_script(
            'splide_js',
            MEDYA_EDUARTE_URL . 'assets/libs/splide/splide.min.js',
            [],
            '4.1.4',
            true
        );

        // Registra o CSS customizado do slideshow
        wp_register_style(
            'medya-eduarte-slideshow_css',
            MEDYA_EDUARTE_URL . 'assets/slideshow.css',
            ['splide'],
            $this->version
        );

        // Registra o JS customizado do slideshow
        wp_register_script(
            'medya-eduarte-slideshow_js',
            MEDYA_EDUARTE_URL . 'assets/slideshow.js',
            ['splide', 'jquery'],
            $this->version,
            true
        );
    }
}
