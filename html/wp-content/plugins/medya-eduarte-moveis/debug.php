<?php
/**
 * Script de Debug para MEDYA Eduarte Móveis
 * 
 * Execute este arquivo via WP-CLI ou adicione ao functions.php temporariamente
 * para diagnosticar problemas com o plugin.
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Função para debug das opções do plugin
 */
function medya_eduarte_debug_options() {
    echo "<h3>MEDYA Eduarte Móveis - Debug</h3>";
    
    // Verifica se o plugin está ativo
    if (!class_exists('Medya_Eduarte_Moveis')) {
        echo "<p style='color: red;'>❌ Plugin não está ativo!</p>";
        return;
    }
    
    echo "<p style='color: green;'>✅ Plugin está ativo</p>";
    
    // Verifica dependências
    echo "<h4>Dependências:</h4>";
    echo "<ul>";
    echo "<li>MEDYA Core: " . (did_action('medya_core/loaded') ? '✅ Ativo' : '❌ Inativo') . "</li>";
    echo "<li>Elementor: " . (did_action('elementor/loaded') ? '✅ Ativo' : '❌ Inativo') . "</li>";
    echo "<li>ACF: " . (function_exists('get_field') ? '✅ Ativo' : '❌ Inativo') . "</li>";
    echo "</ul>";
    
    // Verifica opções
    echo "<h4>Opções do Plugin:</h4>";
    
    $sections = get_option('medya_eduarte_sections');
    $post_type = get_option('medya_eduarte_post_type');
    
    echo "<p><strong>Tipo de Post:</strong> " . esc_html($post_type ?: 'Não definido') . "</p>";
    
    echo "<p><strong>Seções (raw):</strong></p>";
    echo "<pre>" . print_r($sections, true) . "</pre>";
    
    echo "<p><strong>Tipo da variável seções:</strong> " . gettype($sections) . "</p>";
    
    if (is_array($sections)) {
        echo "<p><strong>Número de seções:</strong> " . count($sections) . "</p>";
    }
    
    // Testa método get_sections
    echo "<h4>Teste do método get_sections():</h4>";
    try {
        $processed_sections = Medya_Eduarte_Moveis::get_sections();
        echo "<p><strong>Seções processadas:</strong></p>";
        echo "<pre>" . print_r($processed_sections, true) . "</pre>";
        echo "<p style='color: green;'>✅ Método funcionando</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erro no método: " . $e->getMessage() . "</p>";
    }
    
    // Testa opções de seções
    echo "<h4>Teste do método get_sections_options():</h4>";
    try {
        $options = Medya_Eduarte_Moveis::get_sections_options();
        echo "<p><strong>Opções para select:</strong></p>";
        echo "<pre>" . print_r($options, true) . "</pre>";
        echo "<p style='color: green;'>✅ Método funcionando</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erro no método: " . $e->getMessage() . "</p>";
    }
    
    // Verifica posts do tipo selecionado
    if ($post_type) {
        echo "<h4>Posts do tipo '{$post_type}':</h4>";
        $posts = get_posts([
            'post_type' => $post_type,
            'numberposts' => 5,
            'post_status' => 'publish'
        ]);
        
        if ($posts) {
            echo "<ul>";
            foreach ($posts as $post) {
                echo "<li>ID: {$post->ID} - {$post->post_title}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>Nenhum post encontrado</p>";
        }
    }
    
    // Verifica se há erros de PHP
    echo "<h4>Últimos erros de PHP:</h4>";
    $errors = error_get_last();
    if ($errors) {
        echo "<pre>" . print_r($errors, true) . "</pre>";
    } else {
        echo "<p style='color: green;'>✅ Nenhum erro recente</p>";
    }
}

/**
 * Função para corrigir opções corrompidas
 */
function medya_eduarte_fix_options() {
    echo "<h3>Corrigindo opções...</h3>";
    
    // Corrige seções
    $sections = get_option('medya_eduarte_sections');
    if (!is_array($sections)) {
        update_option('medya_eduarte_sections', []);
        echo "<p style='color: green;'>✅ Opção 'seções' corrigida</p>";
    }
    
    // Corrige post type
    $post_type = get_option('medya_eduarte_post_type');
    if (empty($post_type)) {
        update_option('medya_eduarte_post_type', 'post');
        echo "<p style='color: green;'>✅ Opção 'post_type' corrigida</p>";
    }
    
    echo "<p><strong>Opções corrigidas com sucesso!</strong></p>";
}

/**
 * Função para criar seções de teste
 */
function medya_eduarte_create_test_sections() {
    echo "<h3>Criando seções de teste...</h3>";
    
    $test_sections = [
        [
            'id' => 'section_1',
            'name' => 'Sala de Estar',
            'post_id' => '',
            'order' => 1
        ],
        [
            'id' => 'section_2',
            'name' => 'Quarto',
            'post_id' => '',
            'order' => 2
        ],
        [
            'id' => 'section_3',
            'name' => 'Cozinha',
            'post_id' => '',
            'order' => 3
        ]
    ];
    
    update_option('medya_eduarte_sections', $test_sections);
    echo "<p style='color: green;'>✅ Seções de teste criadas</p>";
    
    // Mostra as seções criadas
    medya_eduarte_debug_options();
}

// Para usar via WP-CLI:
// wp eval-file debug.php

// Para usar no admin (adicione temporariamente ao functions.php):
/*
add_action('admin_init', function() {
    if (isset($_GET['medya_debug'])) {
        medya_eduarte_debug_options();
        exit;
    }
    if (isset($_GET['medya_fix'])) {
        medya_eduarte_fix_options();
        exit;
    }
    if (isset($_GET['medya_test'])) {
        medya_eduarte_create_test_sections();
        exit;
    }
});

// URLs para usar:
// /wp-admin/?medya_debug=1
// /wp-admin/?medya_fix=1  
// /wp-admin/?medya_test=1
*/
