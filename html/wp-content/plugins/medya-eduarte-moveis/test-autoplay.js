/* Teste de Autoplay - MEDYA Eduarte Móveis */

(function($) {
    'use strict';

    function testAutoplay() {
        console.log('=== TESTE DE AUTOPLAY INICIADO ===');
        
        const slideshows = $('.medya-eduarte-slideshow');
        console.log('Slideshows encontrados:', slideshows.length);
        
        slideshows.each(function(index) {
            const $slideshow = $(this);
            const swiper = $slideshow.data('swiper');
            
            console.log(`\n--- Slideshow ${index + 1} ---`);
            
            if (!swiper) {
                console.error('Swiper não encontrado!');
                return;
            }
            
            console.log('Swiper instance:', swiper);
            console.log('Autoplay config:', swiper.params.autoplay);
            console.log('Autoplay object:', swiper.autoplay);
            
            if (swiper.autoplay) {
                console.log('Autoplay running:', swiper.autoplay.running);
                console.log('Autoplay paused:', swiper.autoplay.paused);
                
                // Testa métodos do autoplay
                console.log('Testando métodos do autoplay...');
                
                setTimeout(() => {
                    console.log('Parando autoplay...');
                    swiper.autoplay.stop();
                }, 2000);
                
                setTimeout(() => {
                    console.log('Reiniciando autoplay...');
                    swiper.autoplay.start();
                }, 4000);
                
                setTimeout(() => {
                    console.log('Status após restart:', swiper.autoplay.running);
                }, 5000);
            } else {
                console.error('Autoplay não está configurado!');
            }
            
            // Monitora mudanças de slide
            let slideChangeCount = 0;
            swiper.on('slideChange', function() {
                slideChangeCount++;
                console.log(`Mudança de slide #${slideChangeCount}:`, {
                    activeIndex: this.activeIndex,
                    autoplayRunning: this.autoplay ? this.autoplay.running : 'N/A',
                    timestamp: new Date().toLocaleTimeString()
                });
            });
            
            // Monitora eventos de autoplay
            swiper.on('autoplayStart', function() {
                console.log('🟢 Autoplay INICIADO:', new Date().toLocaleTimeString());
            });
            
            swiper.on('autoplayStop', function() {
                console.log('🔴 Autoplay PARADO:', new Date().toLocaleTimeString());
            });
            
            swiper.on('autoplayPause', function() {
                console.log('⏸️ Autoplay PAUSADO:', new Date().toLocaleTimeString());
            });
            
            swiper.on('autoplayResume', function() {
                console.log('▶️ Autoplay RETOMADO:', new Date().toLocaleTimeString());
            });
        });
        
        console.log('=== FIM DO TESTE ===');
    }
    
    function forceRestartAutoplay() {
        console.log('Forçando restart do autoplay...');
        
        $('.medya-eduarte-slideshow').each(function() {
            const swiper = $(this).data('swiper');
            if (swiper && swiper.autoplay) {
                console.log('Reiniciando autoplay para slideshow...');
                swiper.autoplay.stop();
                setTimeout(() => {
                    swiper.autoplay.start();
                    console.log('Autoplay reiniciado!');
                }, 500);
            }
        });
    }
    
    function monitorAutoplayStatus() {
        console.log('Iniciando monitoramento contínuo do autoplay...');
        
        setInterval(() => {
            $('.medya-eduarte-slideshow').each(function(index) {
                const swiper = $(this).data('swiper');
                if (swiper && swiper.autoplay) {
                    const status = {
                        slideshow: index + 1,
                        activeIndex: swiper.activeIndex,
                        running: swiper.autoplay.running,
                        paused: swiper.autoplay.paused,
                        timestamp: new Date().toLocaleTimeString()
                    };
                    
                    if (!swiper.autoplay.running) {
                        console.warn('⚠️ Autoplay parado detectado:', status);
                    } else {
                        console.log('✅ Autoplay funcionando:', status);
                    }
                }
            });
        }, 3000);
    }
    
    function debugSwiperConfig() {
        console.log('=== DEBUG CONFIGURAÇÃO SWIPER ===');
        
        $('.medya-eduarte-slideshow').each(function(index) {
            const $slideshow = $(this);
            const swiper = $slideshow.data('swiper');
            const settings = $slideshow.attr('data-settings');
            
            console.log(`Slideshow ${index + 1}:`);
            console.log('Data settings:', settings);
            
            if (settings) {
                try {
                    const parsedSettings = JSON.parse(settings);
                    console.log('Parsed settings:', parsedSettings);
                } catch (e) {
                    console.error('Erro ao parsear settings:', e);
                }
            }
            
            if (swiper) {
                console.log('Swiper params:', swiper.params);
                console.log('Swiper autoplay params:', swiper.params.autoplay);
            }
        });
    }
    
    function testManualSlideChange() {
        console.log('Testando mudança manual de slides...');
        
        const swiper = $('.medya-eduarte-slideshow').first().data('swiper');
        if (swiper) {
            let currentSlide = 0;
            const totalSlides = swiper.slides.length;
            
            const interval = setInterval(() => {
                currentSlide = (currentSlide + 1) % totalSlides;
                console.log(`Mudando para slide ${currentSlide}...`);
                swiper.slideTo(currentSlide);
                
                if (currentSlide === 0) {
                    clearInterval(interval);
                    console.log('Teste de mudança manual concluído');
                }
            }, 2000);
        }
    }
    
    // Adiciona controles de debug
    function addDebugControls() {
        if ($('#autoplay-debug-controls').length) return;
        
        const controls = `
            <div id="autoplay-debug-controls" style="
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0,0,0,0.9);
                color: white;
                padding: 15px;
                border-radius: 5px;
                z-index: 9999;
                font-family: monospace;
                font-size: 12px;
                max-width: 300px;
            ">
                <h4 style="margin: 0 0 10px 0; color: #fff;">Autoplay Debug</h4>
                <button onclick="testAutoplay()" style="margin: 2px; padding: 5px; width: 100%;">Testar Autoplay</button>
                <button onclick="forceRestartAutoplay()" style="margin: 2px; padding: 5px; width: 100%;">Forçar Restart</button>
                <button onclick="monitorAutoplayStatus()" style="margin: 2px; padding: 5px; width: 100%;">Monitorar Status</button>
                <button onclick="debugSwiperConfig()" style="margin: 2px; padding: 5px; width: 100%;">Debug Config</button>
                <button onclick="testManualSlideChange()" style="margin: 2px; padding: 5px; width: 100%;">Teste Manual</button>
                <button onclick="jQuery('#autoplay-debug-controls').remove()" style="margin: 2px; padding: 5px; width: 100%;">Fechar</button>
            </div>
        `;
        
        $('body').append(controls);
        
        // Expõe funções globalmente
        window.testAutoplay = testAutoplay;
        window.forceRestartAutoplay = forceRestartAutoplay;
        window.monitorAutoplayStatus = monitorAutoplayStatus;
        window.debugSwiperConfig = debugSwiperConfig;
        window.testManualSlideChange = testManualSlideChange;
    }
    
    // Inicializa quando DOM estiver pronto
    $(document).ready(function() {
        setTimeout(() => {
            addDebugControls();
            console.log('Debug de autoplay carregado. Controles no canto superior esquerdo.');
            
            // Executa teste inicial automaticamente
            setTimeout(testAutoplay, 1000);
        }, 2000);
    });

})(jQuery);

// Para usar este debug:
// 1. Cole este código no console da página
// 2. Use os controles no canto superior esquerdo
// 3. Ou execute as funções diretamente:
//    - testAutoplay()
//    - forceRestartAutoplay()
//    - monitorAutoplayStatus()
//    - debugSwiperConfig()
//    - testManualSlideChange()
