<?php
/**
 * Debug para Submit do Formulário - MEDYA Eduarte Móveis
 * 
 * Adicione este código temporariamente ao functions.php para debug
 */

if (!defined('ABSPATH')) {
    exit;
}

// Debug do submit do formulário
add_action('admin_init', function() {
    if (isset($_POST['option_page']) && $_POST['option_page'] === 'medya_eduarte_group') {
        error_log('=== MEDYA EDUARTE DEBUG SUBMIT ===');
        error_log('POST completo: ' . print_r($_POST, true));
        error_log('Seções enviadas: ' . ($_POST['medya_eduarte_sections'] ?? 'NÃO ENVIADO'));
        error_log('Post type enviado: ' . ($_POST['medya_eduarte_post_type'] ?? 'NÃO ENVIADO'));
        error_log('=== FIM DEBUG SUBMIT ===');
    }
}, 5); // Prioridade baixa para executar antes do handler do plugin

// Debug das opções após salvar
add_action('updated_option', function($option_name, $old_value, $value) {
    if (strpos($option_name, 'medya_eduarte') !== false) {
        error_log("Opção atualizada: {$option_name}");
        error_log("Valor antigo: " . print_r($old_value, true));
        error_log("Valor novo: " . print_r($value, true));
    }
}, 10, 3);

// Debug específico para as seções
add_action('admin_notices', function() {
    if (isset($_GET['page']) && $_GET['page'] === 'medya-eduarte-moveis') {
        $sections = get_option('medya_eduarte_sections', []);
        $post_type = get_option('medya_eduarte_post_type', 'post');
        
        echo '<div class="notice notice-info">';
        echo '<h4>Debug Info:</h4>';
        echo '<p><strong>Post Type atual:</strong> ' . esc_html($post_type) . '</p>';
        echo '<p><strong>Seções salvas:</strong> ' . count($sections) . '</p>';
        if (!empty($sections)) {
            echo '<details><summary>Ver seções</summary>';
            echo '<pre>' . esc_html(print_r($sections, true)) . '</pre>';
            echo '</details>';
        }
        echo '</div>';
    }
});

// Adiciona JavaScript para debug do submit
add_action('admin_footer', function() {
    if (isset($_GET['page']) && $_GET['page'] === 'medya-eduarte-moveis') {
        ?>
        <script>
        // Intercepta o submit para debug
        jQuery('form').on('submit', function(e) {
            console.log('=== DEBUG SUBMIT ===');
            console.log('Formulário sendo submetido');
            
            // Mostra todos os campos do formulário
            const formData = new FormData(this);
            for (let [key, value] of formData.entries()) {
                console.log(key + ':', value);
            }
            
            // Verifica especificamente o campo de seções
            const sectionsField = jQuery('#sections-data');
            console.log('Campo seções existe:', sectionsField.length > 0);
            console.log('Nome do campo:', sectionsField.attr('name'));
            console.log('Valor do campo:', sectionsField.val());
            
            console.log('=== FIM DEBUG SUBMIT ===');
            
            // Permite o submit continuar
            return true;
        });
        
        // Debug do estado atual
        console.log('=== DEBUG ESTADO INICIAL ===');
        console.log('Campo sections-data:', jQuery('#sections-data').val());
        console.log('Objeto medyaEduarte:', typeof medyaEduarte !== 'undefined' ? medyaEduarte : 'UNDEFINED');
        console.log('=== FIM DEBUG INICIAL ===');
        </script>
        <?php
    }
});

// Função para verificar se os dados estão sendo salvos corretamente
function medya_eduarte_verify_save() {
    $sections = get_option('medya_eduarte_sections', []);
    $post_type = get_option('medya_eduarte_post_type', 'post');
    
    echo "<h3>Verificação de Dados Salvos</h3>";
    echo "<p><strong>Post Type:</strong> " . esc_html($post_type) . "</p>";
    echo "<p><strong>Número de seções:</strong> " . count($sections) . "</p>";
    
    if (!empty($sections)) {
        echo "<h4>Seções:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Nome</th><th>Post ID</th><th>Ordem</th></tr>";
        foreach ($sections as $section) {
            echo "<tr>";
            echo "<td>" . esc_html($section['id'] ?? 'N/A') . "</td>";
            echo "<td>" . esc_html($section['name'] ?? 'N/A') . "</td>";
            echo "<td>" . esc_html($section['post_id'] ?? 'N/A') . "</td>";
            echo "<td>" . esc_html($section['order'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// Para usar via URL: /wp-admin/admin.php?page=medya-eduarte-moveis&verify=1
add_action('admin_init', function() {
    if (isset($_GET['verify']) && $_GET['page'] === 'medya-eduarte-moveis') {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-info">';
            medya_eduarte_verify_save();
            echo '</div>';
        });
    }
});

// Adiciona botão de verificação na página
add_action('admin_footer', function() {
    if (isset($_GET['page']) && $_GET['page'] === 'medya-eduarte-moveis') {
        $verify_url = add_query_arg('verify', '1');
        echo '<div style="margin: 20px 0;">';
        echo '<a href="' . esc_url($verify_url) . '" class="button">Verificar Dados Salvos</a>';
        echo '</div>';
    }
});
