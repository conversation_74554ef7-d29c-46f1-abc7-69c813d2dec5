<?php
/**
 * Debug específico para o Elementor - MEDYA Eduarte Móveis
 * 
 * Adicione este código temporariamente ao functions.php para debug do editor
 */

if (!defined('ABSPATH')) {
    exit;
}

// Debug específico para o editor do Elementor
add_action('elementor/editor/before_enqueue_scripts', function() {
    error_log('MEDYA Eduarte: Editor do Elementor carregando...');
    
    if (class_exists('Medya_Eduarte_Moveis')) {
        $sections = Medya_Eduarte_Moveis::get_sections();
        $options = Medya_Eduarte_Moveis::get_sections_options();
        
        error_log('MEDYA Eduarte: Seções no editor: ' . print_r($sections, true));
        error_log('MEDYA Eduarte: Opções no editor: ' . print_r($options, true));
    }
});

// Debug quando widget é renderizado no editor
add_action('elementor/widget/render_content', function($content, $widget) {
    if ($widget->get_name() === 'medya-eduarte-slideshow') {
        error_log('MEDYA Eduarte: Widget sendo renderizado no editor');
        error_log('MEDYA Eduarte: Settings: ' . print_r($widget->get_settings(), true));
    }
    return $content;
}, 10, 2);

// Adiciona JavaScript para debug no editor
add_action('elementor/editor/footer', function() {
    ?>
    <script>
    console.log('MEDYA Eduarte: Editor do Elementor carregado');
    
    // Debug quando widget é adicionado
    elementor.hooks.addAction('panel/open_editor/widget/medya-eduarte-slideshow', function(panel, model, view) {
        console.log('MEDYA Eduarte: Widget aberto no editor');
        console.log('Model:', model);
        console.log('Settings:', model.get('settings').toJSON());
    });
    
    // Debug quando configurações mudam
    elementor.hooks.addAction('panel/open_editor/widget', function(panel, model, view) {
        if (model.get('widgetType') === 'medya-eduarte-slideshow') {
            console.log('MEDYA Eduarte: Configurações do widget:', model.get('settings').toJSON());
        }
    });
    </script>
    <?php
});

// Função para testar manualmente as seções
function medya_eduarte_test_sections_in_editor() {
    if (!class_exists('Medya_Eduarte_Moveis')) {
        return 'Plugin não ativo';
    }
    
    $sections = Medya_Eduarte_Moveis::get_sections();
    $options = Medya_Eduarte_Moveis::get_sections_options();
    
    $output = "<h3>Debug das Seções no Editor</h3>";
    
    $output .= "<h4>Seções Raw:</h4>";
    $output .= "<pre>" . print_r($sections, true) . "</pre>";
    
    $output .= "<h4>Opções para Select:</h4>";
    $output .= "<pre>" . print_r($options, true) . "</pre>";
    
    $output .= "<h4>Teste de cada seção:</h4>";
    foreach ($sections as $section) {
        $output .= "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
        $output .= "<strong>Seção: " . esc_html($section['name']) . " (ID: " . esc_html($section['id']) . ")</strong><br>";
        $output .= "Post ID: " . esc_html($section['post_id'] ?? 'Vazio') . "<br>";
        
        if (!empty($section['post_id'])) {
            $post = get_post($section['post_id']);
            if ($post) {
                $output .= "Post: " . esc_html($post->post_title) . " (Status: " . esc_html($post->post_status) . ")<br>";
                
                $gallery = get_field('galeria', $section['post_id']);
                $output .= "Campo galeria: " . (is_array($gallery) ? count($gallery) . " itens" : "Vazio/Inválido") . "<br>";
                
                $images = Medya_Eduarte_Moveis::get_section_images($section['id']);
                $output .= "Imagens processadas: " . count($images) . "<br>";
                
                if (!empty($images)) {
                    $output .= "<div style='display: flex; gap: 5px; margin: 5px 0;'>";
                    foreach (array_slice($images, 0, 3) as $image) {
                        $output .= "<img src='" . esc_url($image['url']) . "' style='width: 50px; height: 50px; object-fit: cover;'>";
                    }
                    $output .= "</div>";
                }
            } else {
                $output .= "<span style='color: red;'>Post não existe!</span><br>";
            }
        }
        
        $output .= "</div>";
    }
    
    return $output;
}

// Adiciona página de teste no admin
add_action('admin_menu', function() {
    add_submenu_page(
        'tools.php',
        'Debug Elementor Eduarte',
        'Debug Elementor Eduarte',
        'manage_options',
        'debug-elementor-eduarte',
        function() {
            echo '<div class="wrap">';
            echo '<h1>Debug Elementor - MEDYA Eduarte Móveis</h1>';
            echo medya_eduarte_test_sections_in_editor();
            echo '</div>';
        }
    );
});

// Adiciona notice com link para debug
add_action('admin_notices', function() {
    if (isset($_GET['action']) && $_GET['action'] === 'elementor') {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>MEDYA Eduarte Debug:</strong> ';
        echo '<a href="' . admin_url('tools.php?page=debug-elementor-eduarte') . '" target="_blank">Testar Seções</a> | ';
        echo '<a href="' . admin_url('admin.php?page=medya-eduarte-moveis') . '" target="_blank">Gerenciar Seções</a>';
        echo '</p>';
        echo '</div>';
    }
});

// Hook para limpar cache do Elementor quando seções são salvas
add_action('medya_eduarte_sections_saved', function($sections) {
    // Limpa cache do Elementor
    if (class_exists('\Elementor\Plugin')) {
        \Elementor\Plugin::$instance->files_manager->clear_cache();
    }
    
    error_log('MEDYA Eduarte: Cache do Elementor limpo após salvar seções');
});

// Para usar:
// 1. Adicione ao functions.php: include_once(WP_PLUGIN_DIR . '/medya-eduarte-moveis/debug-elementor.php');
// 2. Vá para Ferramentas > Debug Elementor Eduarte
// 3. Ou verifique os logs do WordPress
// 4. Ou use o console do navegador no editor do Elementor
