/**
 * MEDYA Eduarte Móveis - Slideshow com Splide
 */
(function ($) {
    'use strict';

    class MedyaEduarteSlideshowHandler extends elementorModules.frontend.handlers.Base {
        getDefaultSettings() {
            return {
                selectors: {
                    slideshow: '.medya-eduarte-slideshow'
                }
            };
        }

        getDefaultElements() {
            const selectors = this.getSettings('selectors');
            return {
                $slideshow: this.$element.find(selectors.slideshow)
            };
        }

        bindEvents() {
            this.initSlideshow();
        }

        async initSlideshow() {
            const $slideshow = this.elements.$slideshow;

            if (!$slideshow.length) {
                console.warn('MEDYA Slideshow: Element not found');
                return;
            }

            try {
                // Obtém configurações
                const settings = JSON.parse($slideshow.attr('data-settings') || '{}');
                console.log('MEDYA Slideshow: Initializing with Splide, settings:', settings);

                // Carrega Splide se não estiver disponível
                await this.loadSplide();

                // Converte estrutura para Splide
                this.convertToSplideStructure($slideshow);

                // Configuração do Splide
                const splideConfig = this.getSplideConfig(settings);

                // Inicializa Splide
                this.splide = new Splide($slideshow[0], splideConfig);

                // Configura eventos
                this.setupSplideEvents(settings);

                // Monta o Splide
                this.splide.mount();

                // Salva referência
                $slideshow.data('splide', this.splide);

                // Configurações pós-inicialização
                this.setupPostInit(settings);

                console.log('MEDYA Slideshow: Successfully initialized with Splide');

            } catch (error) {
                console.error('MEDYA Slideshow: Initialization error:', error);
                $slideshow.removeClass('loading').addClass('error');
            }
        }

        async loadSplide() {
            // Verifica se Splide já está carregado
            if (window.Splide) {
                return;
            }

            // Carrega CSS do Splide
            if (!document.querySelector('link[href*="splide"]')) {
                const cssLink = document.createElement('link');
                cssLink.rel = 'stylesheet';
                cssLink.href = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css';
                document.head.appendChild(cssLink);
            }

            // Carrega JS do Splide
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js';
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        convertToSplideStructure($slideshow) {
            // Remove classes do Swiper
            $slideshow.removeClass('swiper swiper-initialized swiper-horizontal swiper-pointer-events swiper-watch-progress medya-custom-fade');

            // Adiciona classes do Splide
            $slideshow.addClass('splide');

            // Converte wrapper
            const $wrapper = $slideshow.find('.swiper-wrapper');
            $wrapper.removeClass('swiper-wrapper').addClass('splide__track');

            // Cria lista de slides
            const $slidesList = $('<ul class="splide__list"></ul>');

            // Converte slides
            const $slides = $slideshow.find('.swiper-slide');
            $slides.each(function () {
                const $slide = $(this);

                // Remove classes do Swiper
                $slide.removeClass('swiper-slide swiper-slide-active swiper-slide-next swiper-slide-prev swiper-slide-duplicate swiper-slide-duplicate-active swiper-slide-duplicate-next');

                // Adiciona classe do Splide
                $slide.addClass('splide__slide');

                // Remove estilos inline problemáticos
                $slide.removeAttr('style');

                // Adiciona à lista
                $slidesList.append($slide);
            });

            // Substitui wrapper
            $wrapper.empty().append($slidesList);

            console.log('MEDYA Slideshow: Structure converted to Splide');
        }

        getSplideConfig(settings) {
            const config = {
                type: 'loop',
                perPage: 1,
                perMove: 1,
                gap: 0,
                arrows: false,
                pagination: false,
                drag: true,
                keyboard: true,
                speed: settings.speed || 500,
                easing: 'ease',
                autoWidth: false,
                autoHeight: false,
                fixedWidth: null,
                fixedHeight: null,
                cover: true,
                focus: 'center',
                isNavigation: false,
                updateOnMove: true,
                throttle: 100,
                destroy: false,
                breakpoints: {}
            };

            // Configuração de autoplay
            if (settings.autoplay !== false) {
                config.autoplay = true;
                config.interval = settings.duration || 5000;
                config.pauseOnHover = false;
                config.pauseOnFocus = false;
                config.resetProgress = false;
            }

            // Configuração de transição
            if (settings.effect === 'fade') {
                config.type = 'fade';
                config.speed = settings.speed || 500;
            }

            console.log('MEDYA Slideshow: Splide config:', config);
            return config;
        }

        setupSplideEvents(settings) {
            // Evento de mudança de slide
            this.splide.on('moved', (newIndex, prevIndex) => {
                console.log('MEDYA Slideshow: Slide changed from', prevIndex, 'to', newIndex);
                this.ensureSlideVisibility();
                this.handleKenBurns(settings);
            });

            // Evento de inicialização
            this.splide.on('mounted', () => {
                console.log('MEDYA Slideshow: Splide mounted');
                this.ensureSlideVisibility();
                this.handleKenBurns(settings);
            });

            // Eventos de autoplay
            this.splide.on('autoplay:play', () => {
                console.log('MEDYA Slideshow: Autoplay started');
            });

            this.splide.on('autoplay:pause', () => {
                console.log('MEDYA Slideshow: Autoplay paused');
            });
        }

        setupPostInit(settings) {
            // Garante visibilidade inicial
            setTimeout(() => {
                this.ensureSlideVisibility();
                this.handleKenBurns(settings);
            }, 100);

            // Remove loading
            this.elements.$slideshow.removeClass('loading');
        }

        ensureSlideVisibility() {
            const $slideshow = this.elements.$slideshow;

            // Remove aria-hidden de slides ativos
            const $activeSlides = $slideshow.find('.splide__slide.is-active');
            $activeSlides.removeAttr('aria-hidden');

            // Força visibilidade via CSS
            $activeSlides.css({
                'opacity': '1',
                'visibility': 'visible',
                'display': 'block'
            });

            // Para fade effect, esconde slides não ativos
            if ($slideshow.hasClass('splide--fade')) {
                const $inactiveSlides = $slideshow.find('.splide__slide:not(.is-active)');
                $inactiveSlides.css('opacity', '0');
            }

            console.log('MEDYA Slideshow: Slide visibility ensured for', $activeSlides.length, 'active slides');
        }

        handleKenBurns(settings) {
            if (!settings.kenBurns) return;

            const $slideshow = this.elements.$slideshow;
            const $activeSlide = $slideshow.find('.splide__slide.is-active');
            const $kenBurnsElement = $activeSlide.find('.medya-ken-burns-effect');

            if ($kenBurnsElement.length) {
                // Remove animação anterior de todos os slides
                $slideshow.find('.medya-ken-burns-effect').removeClass('ken-burns-active');

                // Força reflow
                $kenBurnsElement[0].offsetHeight;

                // Adiciona animação ao slide ativo
                $kenBurnsElement.addClass('ken-burns-active');

                console.log('MEDYA Slideshow: Ken Burns applied to active slide');
            }
        }

        onDestroy() {
            if (this.splide) {
                this.splide.destroy();
                this.splide = null;
            }
        }
    }

    // Registra o handler
    $(window).on('elementor/frontend/init', () => {
        elementorFrontend.hooks.addAction('frontend/element_ready/medya-eduarte-slideshow.default', ($scope) => {
            new MedyaEduarteSlideshowHandler({ $element: $scope });
        });
    });

})(jQuery);
