/**
 * MEDYA Eduarte Móveis - Slideshow Simplificado
 */
(function ($) {
    'use strict';

    function initializeSlideshow(element) {
        const $slideshow = $(element);

        if (!$slideshow.length || $slideshow.data('initialized')) {
            return;
        }

        // Aguarda Splide
        if (typeof Splide === 'undefined') {
            setTimeout(() => initializeSlideshow(element), 100);
            return;
        }

        try {
            const settings = JSON.parse($slideshow.attr('data-settings') || '{}');

            const options = {
                type: settings.effect === 'fade' ? 'fade' : 'loop',
                autoplay: settings.autoplay !== false,
                interval: settings.duration || 5000,
                speed: settings.speed || 400,
                rewind: true,
                arrows: false,
                pagination: false,
                drag: true
            };

            const splide = new Splide(element, options);

            // Ken <PERSON>
            if (settings.kenBurns) {
                splide.on('mounted moved', () => {
                    $slideshow.find('.medya-ken-burns-effect').removeClass('ken-burns-active');
                    $slideshow.find('.splide__slide.is-active .medya-ken-burns-effect').addClass('ken-burns-active');
                });
            }

            splide.mount();
            $slideshow.data('initialized', true);

        } catch (error) {
            console.error('MEDYA Slideshow error:', error);
        }
    }

    // Elementor frontend
    $(window).on('elementor/frontend/init', () => {
        elementorFrontend.hooks.addAction('frontend/element_ready/medya-eduarte-slideshow.default', ($scope) => {
            const slideshow = $scope.find('.medya-eduarte-slideshow')[0];
            if (slideshow) initializeSlideshow(slideshow);
        });
    });

    // Fallback universal
    $(document).ready(() => {
        setTimeout(() => {
            $('.medya-eduarte-slideshow').each(function () {
                initializeSlideshow(this);
            });
        }, 1000);
    });

})(jQuery);