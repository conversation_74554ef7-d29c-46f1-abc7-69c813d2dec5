/**
 * MEDYA Eduarte Móveis - Slideshow
 */
(function ($) {
    'use strict';

    function initializeSlideshow(element) {
        const $slideshow = $(element);

        if (!$slideshow.length || $slideshow.data('initialized')) {
            return;
        }

        // Verifica se a estrutura está presente
        const track = $slideshow.find('.splide__track');
        const list = $slideshow.find('.splide__list');
        const slides = $slideshow.find('.splide__slide');

        if (!track.length || !list.length || !slides.length) {
            setTimeout(() => initializeSlideshow(element), 200);
            return;
        }

        // Aguarda Splide
        if (typeof Splide === 'undefined') {
            // Se estamos no editor e Splide não está carregado, carrega via CDN
            if (typeof elementor !== 'undefined' && elementor.config) {
                loadSplideFromCDN(() => initializeSlideshow(element));
                return;
            }
            setTimeout(() => initializeSlideshow(element), 100);
            return;
        }

        try {
            const settings = JSON.parse($slideshow.attr('data-settings') || '{}');

            const options = {
                type: settings.effect === 'fade' ? 'fade' : 'loop',
                autoplay: settings.autoplay !== false,
                interval: settings.duration || 5000,
                rewind: settings.loop !== false,
                speed: settings.speed || 400,
                arrows: false,
                pagination: false,
                drag: true
            };

            const splide = new Splide(element, options);
            splide.mount();
            $slideshow.data('initialized', true);

        } catch (error) {
            // Silently fail
        }
    }

    function loadSplideFromCDN(callback) {
        // Carrega CSS do Splide
        if (!document.querySelector('link[href*="splide"]')) {
            const css = document.createElement('link');
            css.rel = 'stylesheet';
            css.href = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css';
            document.head.appendChild(css);
        }

        // Carrega JS do Splide
        if (!document.querySelector('script[src*="splide"]')) {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js';
            script.onload = callback;
            script.onerror = () => {
                setTimeout(callback, 500); // Tenta mesmo se falhar
            };
            document.head.appendChild(script);
        } else {
            callback();
        }
    }

    // Elementor frontend
    $(window).on('elementor/frontend/init', () => {
        elementorFrontend.hooks.addAction('frontend/element_ready/medya-eduarte-slideshow.default', ($scope) => {
            const slideshow = $scope.find('.medya-eduarte-slideshow')[0];
            if (slideshow) initializeSlideshow(slideshow);
        });
    });

    // Fallback universal (editor e frontend)
    $(document).ready(() => {
        setTimeout(() => {
            $('.medya-eduarte-slideshow').each(function () {
                initializeSlideshow(this);
            });
        }, 1000);
    });

})(jQuery);