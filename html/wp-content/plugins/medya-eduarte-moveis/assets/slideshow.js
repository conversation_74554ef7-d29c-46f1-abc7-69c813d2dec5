/**
 * MEDYA Eduarte Móveis - Slideshow
 */
(function ($) {
    'use strict';

    function initializeSlideshow(element) {
        const $slideshow = $(element);

        if (!$slideshow.length || $slideshow.data('initialized')) {
            return;
        }

        // Verifica se a estrutura está presente
        const track = $slideshow.find('.splide__track');
        const list = $slideshow.find('.splide__list');
        const slides = $slideshow.find('.splide__slide');

        if (!track.length || !list.length || !slides.length) {
            setTimeout(() => initializeSlideshow(element), 200);
            return;
        }

        // Aguarda Splide
        if (typeof Splide === 'undefined') {
            setTimeout(() => initializeSlideshow(element), 100);
            return;
        }

        try {
            const settings = JSON.parse($slideshow.attr('data-settings') || '{}');

            const options = {
                type: settings.effect === 'fade' ? 'fade' : 'loop',
                autoplay: settings.autoplay !== false,
                interval: settings.duration || 5000,
                rewind: settings.loop !== false,
                speed: settings.speed || 400,
                arrows: false,
                pagination: false,
                drag: true
            };

            const splide = new Splide(element, options);
            splide.mount();
            $slideshow.data('initialized', true);

        } catch (error) {
            // Silently fail
        }
    }

    // Inicialização universal
    $(document).ready(() => {
        setTimeout(() => {
            $('.medya-eduarte-slideshow').each(function () {
                initializeSlideshow(this);
            });
        }, 1000);
    });

})(jQuery);