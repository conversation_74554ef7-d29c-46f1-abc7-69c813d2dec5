/**
 * MEDYA Eduarte Móveis - Slideshow com Splide (Implementação Oficial)
 */
(function ($) {
    'use strict';

    class MedyaEduarteSlideshowHandler extends elementorModules.frontend.handlers.Base {
        getDefaultSettings() {
            return {
                selectors: {
                    slideshow: '.medya-eduarte-slideshow'
                }
            };
        }

        getDefaultElements() {
            const selectors = this.getSettings('selectors');
            return {
                $slideshow: this.$element.find(selectors.slideshow)
            };
        }

        bindEvents() {
            this.initSlideshow();
        }

        initSlideshow() {
            const $slideshow = this.elements.$slideshow;

            if (!$slideshow.length) {
                console.warn('MEDYA Slideshow: Element not found');
                return;
            }

            // Verifica se já foi inicializado
            if ($slideshow.hasClass('splide') && $slideshow.data('splide')) {
                console.log('MEDYA Slideshow: Already initialized');
                return;
            }

            // Aguarda o Splide estar disponível
            if (typeof Splide === 'undefined') {
                console.warn('MEDYA Slideshow: Splide not loaded, retrying...');
                setTimeout(() => this.initSlideshow(), 100);
                return;
            }

            // Verifica se estamos no editor do Elementor
            const isEditor = typeof elementor !== 'undefined' && elementor.config;
            if (isEditor) {
                console.log('MEDYA Slideshow: Initializing in Elementor editor');
            }

            try {
                // Obtém configurações do elemento
                const settings = JSON.parse($slideshow.attr('data-settings') || '{}');
                console.log('MEDYA Slideshow: Initializing with settings:', settings);

                // Configuração do Splide seguindo a documentação oficial
                const splideOptions = this.getSplideOptions(settings);

                // Inicializa Splide exatamente como na documentação
                this.splide = new Splide($slideshow[0], splideOptions);

                // Configura eventos antes do mount
                this.setupEvents(settings);

                // Monta o Splide
                this.splide.mount();

                // Salva referência
                $slideshow.data('splide', this.splide);

                console.log('MEDYA Slideshow: Successfully initialized');

            } catch (error) {
                console.error('MEDYA Slideshow: Initialization error:', error);
                $slideshow.addClass('error');
            }
        }

        getSplideOptions(settings) {
            const options = {
                // Configurações básicas
                type: settings.effect === 'fade' ? 'fade' : 'loop',
                speed: settings.speed || 400,

                // Autoplay
                autoplay: settings.autoplay !== false,
                interval: settings.duration || 5000,
                pauseOnHover: false,
                pauseOnFocus: false,
                resetProgress: false,

                // Navegação
                arrows: false,
                pagination: false,

                // Acessibilidade
                keyboard: false,
                drag: true,

                // Performance
                waitForTransition: false,
                updateOnMove: false
            };

            console.log('MEDYA Slideshow: Splide options:', options);
            return options;
        }

        setupEvents(settings) {
            // Evento após montagem
            this.splide.on('mounted', () => {
                console.log('MEDYA Slideshow: Splide mounted');
                this.handleKenBurns(settings);
            });

            // Evento de mudança de slide
            this.splide.on('moved', (newIndex, prevIndex) => {
                console.log('MEDYA Slideshow: Slide changed from', prevIndex, 'to', newIndex);
                this.handleKenBurns(settings);
            });

            // Eventos de autoplay
            this.splide.on('autoplay:play', () => {
                console.log('MEDYA Slideshow: Autoplay started');
            });

            this.splide.on('autoplay:pause', () => {
                console.log('MEDYA Slideshow: Autoplay paused');
            });

            this.splide.on('autoplay:playing', (rate) => {
                // Rate vai de 0 a 1 indicando o progresso
                // console.log('MEDYA Slideshow: Autoplay progress:', rate);
            });
        }

        handleKenBurns(settings) {
            if (!settings.kenBurns) return;

            const $slideshow = this.elements.$slideshow;

            // Remove animação de todos os elementos Ken Burns
            $slideshow.find('.medya-ken-burns-effect').removeClass('ken-burns-active');

            // Adiciona animação apenas ao slide ativo
            const $activeSlide = $slideshow.find('.splide__slide.is-active');
            const $kenBurnsElement = $activeSlide.find('.medya-ken-burns-effect');

            if ($kenBurnsElement.length) {
                // Força reflow para reiniciar a animação
                $kenBurnsElement[0].offsetHeight;

                // Adiciona a classe de animação
                $kenBurnsElement.addClass('ken-burns-active');

                console.log('MEDYA Slideshow: Ken Burns applied to active slide');
            }
        }

        onDestroy() {
            if (this.splide) {
                this.splide.destroy();
                this.splide = null;
            }
        }
    }

    // Função para inicializar slideshow
    function initializeSlideshow($scope) {
        new MedyaEduarteSlideshowHandler({ $element: $scope });
    }

    // Registra o handler no Elementor (frontend)
    $(window).on('elementor/frontend/init', () => {
        elementorFrontend.hooks.addAction('frontend/element_ready/medya-eduarte-slideshow.default', initializeSlideshow);
    });

    // Inicialização específica para o editor do Elementor
    $(window).on('elementor/editor/init', () => {
        // No editor, aguarda um pouco mais para garantir que tudo esteja carregado
        setTimeout(() => {
            elementor.hooks.addAction('panel/open_editor/widget/medya-eduarte-slideshow', () => {
                setTimeout(() => {
                    $('.medya-eduarte-slideshow').each(function () {
                        const $scope = $(this).closest('.elementor-widget-medya-eduarte-slideshow');
                        if ($scope.length) {
                            initializeSlideshow($scope);
                        }
                    });
                }, 500);
            });
        }, 1000);
    });

    // Fallback: Inicialização direta quando o documento estiver pronto
    $(document).ready(() => {
        // Aguarda um pouco para garantir que o Elementor tenha carregado
        setTimeout(() => {
            $('.medya-eduarte-slideshow').each(function () {
                const $slideshow = $(this);
                const $scope = $slideshow.closest('.elementor-widget-medya-eduarte-slideshow');

                // Só inicializa se ainda não foi inicializado
                if ($scope.length && !$slideshow.hasClass('splide--initialized')) {
                    console.log('MEDYA Slideshow: Fallback initialization');
                    initializeSlideshow($scope);
                }
            });
        }, 2000);
    });

})(jQuery);
