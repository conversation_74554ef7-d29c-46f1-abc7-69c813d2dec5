/**
 * MEDYA Eduarte Móveis - Slideshow Simplificado (<PERSON><PERSON>)
 */
(function ($) {
    'use strict';

    function initializeSlideshow(element) {
        const $slideshow = $(element);

        if (!$slideshow.length || $slideshow.data('initialized')) {
            return;
        }

        // Verifica se a estrutura está presente
        const track = $slideshow.find('.splide__track');
        const list = $slideshow.find('.splide__list');
        const slides = $slideshow.find('.splide__slide');

        if (!track.length || !list.length || !slides.length) {
            console.warn('MEDYA Slideshow: Estrutura incompleta, aguardando...');
            setTimeout(() => initializeSlideshow(element), 200);
            return;
        }

        // Aguarda Splide
        if (typeof Splide === 'undefined') {
            setTimeout(() => initializeSlideshow(element), 100);
            return;
        }

        try {
            const settings = JSON.parse($slideshow.attr('data-settings') || '{}');

            const options = {
                type: settings.effect === 'fade' ? 'fade' : 'loop',
                autoplay: settings.autoplay !== false,
                interval: settings.duration || 5000,
                speed: settings.speed || 400,
                arrows: false,
                pagination: false,
                drag: true
            };

            const splide = new Splide(element, options);
            splide.mount();
            $slideshow.data('initialized', true);

            console.log('MEDYA Slideshow: Inicializado com sucesso');

        } catch (error) {
            console.error('MEDYA Slideshow error:', error);
        }
    }

    // Elementor frontend
    $(window).on('elementor/frontend/init', () => {
        elementorFrontend.hooks.addAction('frontend/element_ready/medya-eduarte-slideshow.default', ($scope) => {
            const slideshow = $scope.find('.medya-eduarte-slideshow')[0];
            if (slideshow) initializeSlideshow(slideshow);
        });
    });

    // Fallback universal (funciona no editor e frontend)
    $(document).ready(() => {
        setTimeout(() => {
            $('.medya-eduarte-slideshow').each(function () {
                initializeSlideshow(this);
            });
        }, 1500); // Aumentei para 1.5s para dar tempo do editor carregar
    });

})(jQuery);