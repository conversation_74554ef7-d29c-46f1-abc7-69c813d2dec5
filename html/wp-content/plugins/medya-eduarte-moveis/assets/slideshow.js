/* MEDYA Eduarte Móveis - Slideshow JavaScript */

(function ($) {
    'use strict';

    class MedyaEduarteSlideshowHandler extends elementorModules.frontend.handlers.Base {
        getDefaultSettings() {
            return {
                selectors: {
                    slideshow: '.medya-eduarte-slideshow',
                    slide: '.medya-eduarte-slide'
                }
            };
        }

        getDefaultElements() {
            const selectors = this.getSettings('selectors');
            return {
                $slideshow: this.$element.find(selectors.slideshow),
                $slides: this.$element.find(selectors.slide)
            };
        }

        bindEvents() {
            // Inicializa quando o elemento é carregado
            this.initSlideshow();
        }

        async initSlideshow() {
            const $slideshow = this.elements.$slideshow;

            if (!$slideshow.length || this.elements.$slides.length <= 1) {
                return;
            }

            // Obtém configurações do elemento
            const settings = JSON.parse($slideshow.attr('data-settings') || '{}');

            // Configurações padrão do Swiper
            const swiperConfig = {
                loop: settings.loop !== false,
                speed: settings.speed || 500,
                effect: settings.effect || 'fade',
                allowTouchMove: true,
                grabCursor: true,
                watchSlidesProgress: true,
                observer: true,
                observeParents: true,
                on: {
                    init: (swiper) => {
                        $slideshow.removeClass('loading');
                        console.log('Swiper inicializado:', swiper);

                        // Força o primeiro slide a ficar visível
                        setTimeout(() => {
                            const activeSlide = swiper.slides[swiper.activeIndex];
                            if (activeSlide) {
                                activeSlide.style.opacity = '1';
                                console.log('Primeiro slide forçado a ficar visível');
                            }
                        }, 100);

                        this.handleKenBurns(settings);
                    },
                    slideChange: (swiper) => {
                        console.log('Slide mudou para:', swiper.activeIndex);
                        console.log('Autoplay status:', swiper.autoplay ? swiper.autoplay.running : 'N/A');
                        this.ensureActiveSlideVisible();
                        this.handleKenBurns(settings);
                    },
                    autoplayStart: () => {
                        console.log('Autoplay iniciado');
                    },
                    autoplayStop: () => {
                        console.log('Autoplay parado');
                    }
                }
            };

            // Configurações de autoplay
            if (settings.autoplay !== false) {
                swiperConfig.autoplay = {
                    delay: settings.duration || 5000,
                    disableOnInteraction: false,
                    pauseOnMouseEnter: false,
                    reverseDirection: false,
                    stopOnLastSlide: false,
                    waitForTransition: true
                };
            }

            // Configurações específicas por efeito
            switch (settings.effect) {
                case 'fade':
                    // Desabilita o fade do Swiper e usa nosso próprio
                    swiperConfig.effect = 'slide';
                    swiperConfig.spaceBetween = 0;
                    swiperConfig.centeredSlides = true;
                    // Adiciona classe para nosso fade customizado
                    $slideshow.addClass('medya-custom-fade');
                    break;

                case 'cube':
                    swiperConfig.cubeEffect = {
                        shadow: true,
                        slideShadows: true,
                        shadowOffset: 20,
                        shadowScale: 0.94
                    };
                    break;

                case 'coverflow':
                    swiperConfig.coverflowEffect = {
                        rotate: 50,
                        stretch: 0,
                        depth: 100,
                        modifier: 1,
                        slideShadows: true
                    };
                    break;
            }

            // Adiciona classe do efeito
            $slideshow.addClass('swiper-' + settings.effect);

            try {
                // Inicializa Swiper
                const Swiper = elementorFrontend.utils.swiper;
                this.swiper = await new Swiper($slideshow[0], swiperConfig);

                // Expõe a instância do swiper
                $slideshow.data('swiper', this.swiper);

                // Força visibilidade do slide ativo após inicialização
                setTimeout(() => {
                    this.ensureActiveSlideVisible();
                }, 200);

                // Força visibilidade periodicamente para garantir (menos frequente para não interferir no autoplay)
                this.visibilityInterval = setInterval(() => {
                    this.ensureActiveSlideVisible();
                }, 3000);

                // Intercepta mudanças de estilo do Swiper
                this.interceptSwiperStyles();

                // Monitora e reinicia autoplay se necessário
                if (settings.autoplay !== false) {
                    this.monitorAutoplay(settings);
                }

            } catch (error) {
                console.error('Erro ao inicializar slideshow:', error);
                $slideshow.removeClass('loading').addClass('error');
            }
        }

        interceptSwiperStyles() {
            const $slideshow = this.elements.$slideshow;
            const slides = $slideshow.find('.swiper-slide');

            // Cria um MutationObserver para interceptar mudanças de estilo
            this.styleObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        const slide = mutation.target;
                        if (slide.classList.contains('swiper-slide-active')) {
                            // Se é o slide ativo e tem opacity 0, força para 1
                            if (slide.style.opacity === '0') {
                                slide.style.setProperty('opacity', '1', 'important');
                                console.log('Interceptado: Slide ativo forçado para opacity 1');
                            }
                        }
                    }
                });
            });

            // Observa mudanças de estilo em todos os slides
            slides.each((index, slide) => {
                this.styleObserver.observe(slide, {
                    attributes: true,
                    attributeFilter: ['style']
                });
            });
        }

        monitorAutoplay(settings) {
            if (!this.swiper || !this.swiper.autoplay) return;

            let lastSlideIndex = this.swiper.activeIndex;
            let autoplayStoppedCount = 0;

            this.autoplayMonitor = setInterval(() => {
                if (!this.swiper || !this.swiper.autoplay) return;

                const currentIndex = this.swiper.activeIndex;
                const isAutoplayRunning = this.swiper.autoplay.running;

                // Se o slide não mudou e o autoplay deveria estar rodando
                if (currentIndex === lastSlideIndex && !isAutoplayRunning) {
                    autoplayStoppedCount++;
                    console.log('Autoplay parado detectado, tentativa de restart:', autoplayStoppedCount);

                    if (autoplayStoppedCount >= 2) {
                        // Reinicia o autoplay
                        this.swiper.autoplay.start();
                        console.log('Autoplay reiniciado');
                        autoplayStoppedCount = 0;
                    }
                } else {
                    autoplayStoppedCount = 0;
                }

                lastSlideIndex = currentIndex;
            }, settings.duration || 5000);
        }

        ensureActiveSlideVisible() {
            if (!this.swiper) return;

            const $slideshow = this.elements.$slideshow;

            // Para fade customizado, força visibilidade via CSS classes
            if ($slideshow.hasClass('medya-custom-fade')) {
                const activeSlide = $slideshow.find('.swiper-slide-active')[0];
                const allSlides = $slideshow.find('.swiper-slide');

                // Remove opacity inline de todos os slides
                allSlides.each(function () {
                    this.style.removeProperty('opacity');
                    this.style.removeProperty('transform');
                });

                if (activeSlide) {
                    console.log('Fade customizado: Slide ativo detectado:', activeSlide);
                }
            } else {
                // Para outros efeitos, usa o método original
                const activeSlide = $slideshow.find('.swiper-slide-active')[0];

                if (activeSlide) {
                    activeSlide.style.setProperty('opacity', '1', 'important');
                    activeSlide.style.setProperty('visibility', 'visible', 'important');
                    activeSlide.style.setProperty('display', 'block', 'important');
                    console.log('Slide ativo forçado a ficar visível:', activeSlide);
                }

                $slideshow.find('.swiper-slide:not(.swiper-slide-active)').each(function () {
                    this.style.setProperty('opacity', '0', 'important');
                });
            }
        }

        handleKenBurns(settings) {
            if (!settings.kenBurns) {
                return;
            }

            const $activeSlide = this.elements.$slideshow.find('.swiper-slide-active');
            const $kenBurnsElement = $activeSlide.find('.medya-ken-burns-effect');

            if ($kenBurnsElement.length) {
                // Remove animação anterior
                $kenBurnsElement.removeClass('ken-burns-active');

                // Força reflow
                $kenBurnsElement[0].offsetHeight;

                // Adiciona animação
                $kenBurnsElement.addClass('ken-burns-active');
            }
        }

        onElementChange(propertyName) {
            // Reinicializa se configurações importantes mudaram
            const reinitProps = [
                'selected_section',
                'slide_transition',
                'slideshow_loop',
                'slideshow_autoplay'
            ];

            if (reinitProps.includes(propertyName)) {
                this.destroySlideshow();
                setTimeout(() => {
                    this.initSlideshow();
                }, 100);
            }
        }

        destroySlideshow() {
            if (this.swiper) {
                this.swiper.destroy(true, true);
                this.swiper = null;
            }

            // Limpa o intervalo de visibilidade
            if (this.visibilityInterval) {
                clearInterval(this.visibilityInterval);
                this.visibilityInterval = null;
            }

            // Limpa o observer de estilos
            if (this.styleObserver) {
                this.styleObserver.disconnect();
                this.styleObserver = null;
            }

            // Limpa o monitor de autoplay
            if (this.autoplayMonitor) {
                clearInterval(this.autoplayMonitor);
                this.autoplayMonitor = null;
            }
        }

        onDestroy() {
            this.destroySlideshow();
        }
    }

    // Registra o handler quando o Elementor frontend estiver pronto
    $(window).on('elementor/frontend/init', function () {
        elementorFrontend.hooks.addAction(
            'frontend/element_ready/medya-eduarte-slideshow.default',
            function ($scope) {
                new MedyaEduarteSlideshowHandler({ $element: $scope });
            }
        );
    });

    // Fallback para inicialização manual se necessário
    $(document).ready(function () {
        $('.medya-eduarte-slideshow').each(function () {
            const $slideshow = $(this);

            // Verifica se já foi inicializado
            if ($slideshow.data('swiper')) {
                return;
            }

            // Adiciona classe de loading
            $slideshow.addClass('loading');

            // Inicializa após um pequeno delay
            setTimeout(() => {
                if (!$slideshow.data('swiper')) {
                    const handler = new MedyaEduarteSlideshowHandler({
                        $element: $slideshow.closest('.elementor-widget-medya-eduarte-slideshow')
                    });
                }
            }, 500);
        });
    });

})(jQuery);

// CSS adicional para Ken Burns via JavaScript
const kenBurnsCSS = `
.medya-ken-burns-effect.ken-burns-active {
    animation: kenBurnsZoom 10s ease-in-out forwards;
}

@keyframes kenBurnsZoom {
    0% {
        transform: scale(1) translate(0, 0);
    }
    100% {
        transform: scale(1.1) translate(-2%, -2%);
    }
}
`;

// Adiciona CSS dinamicamente
if (!document.getElementById('medya-ken-burns-css')) {
    const style = document.createElement('style');
    style.id = 'medya-ken-burns-css';
    style.textContent = kenBurnsCSS;
    document.head.appendChild(style);
}
