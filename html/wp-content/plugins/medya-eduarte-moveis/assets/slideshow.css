/* MEDYA Eduarte Móveis - Slideshow com Splide */

/* Container principal */
.medya-eduarte-slideshow {
  position: relative;
  width: 100%;
  height: 50vh;
  min-height: 300px;
  overflow: hidden;
  border-radius: 0;
  background: #f5f5f5;
}

/* Splide track e list */
.medya-eduarte-slideshow .splide__track {
  height: 100%;
}

.medya-eduarte-slideshow .splide__list {
  height: 100%;
  margin: 0;
  padding: 0;
}

/* Slides */
.medya-eduarte-slideshow .splide__slide {
  height: 100%;
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Slide individual */
.medya-eduarte-slide {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Overlay sutil */
.medya-eduarte-slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

/* Conteúdo do slide */
.medya-eduarte-slide-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

/* <PERSON> */
.medya-ken-burns-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: inherit;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  transition: transform 8s ease-in-out;
  transform: scale(1);
}

.medya-ken-burns-effect.ken-burns-active {
  transform: scale(1.1) translate(-2%, -2%);
}

/* Customizações específicas para fade */
.medya-eduarte-slideshow.splide--fade .splide__slide {
  /* Deixa o Splide gerenciar a opacidade naturalmente */
}

/* Estado de erro */
.medya-eduarte-slideshow.error {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 16px;
}

.medya-eduarte-slideshow.error::after {
  content: "Erro ao carregar slideshow";
}

/* Responsivo */
@media (max-width: 768px) {
  .medya-eduarte-slideshow {
    height: 40vh;
    min-height: 250px;
  }
}

@media (max-width: 480px) {
  .medya-eduarte-slideshow {
    height: 30vh;
    min-height: 200px;
  }
}

/* Loading state */
.medya-eduarte-slideshow:not(.splide) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.medya-eduarte-slideshow:not(.splide)::before {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0073aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
