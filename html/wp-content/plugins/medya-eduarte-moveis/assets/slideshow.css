/* MEDYA Eduarte Móveis - Slideshow com Splide */

/* Container principal */
.medya-eduarte-slideshow {
  position: relative;
  width: 100%;
  height: 50vh;
  min-height: 300px;
  overflow: hidden;
  border-radius: 0;
  background: #f5f5f5;
}

/* Splide track e list */
.medya-eduarte-slideshow .splide__track {
  height: 100%;
}

.medya-eduarte-slideshow .splide__list {
  height: 100%;
  margin: 0;
  padding: 0;
}

/* Slides */
.medya-eduarte-slideshow .splide__slide {
  height: 100%;
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Slide individual */
.medya-eduarte-slide {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Customizações específicas para fade */
.medya-eduarte-slideshow.splide--fade .splide__slide {
  /* <PERSON><PERSON>a o Splide gerenciar a opacidade naturalmente */
}

/* Estado de erro */
.medya-eduarte-slideshow.error {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 16px;
}

.medya-eduarte-slideshow.error::after {
  content: "Erro ao carregar slideshow";
}

/* Responsivo */
@media (max-width: 768px) {
  .medya-eduarte-slideshow {
    height: 40vh;
    min-height: 250px;
  }
}

@media (max-width: 480px) {
  .medya-eduarte-slideshow {
    height: 30vh;
    min-height: 200px;
  }
}

/* Loading state */
.medya-eduarte-slideshow:not(.splide) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.medya-eduarte-slideshow:not(.splide)::before {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0073aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
