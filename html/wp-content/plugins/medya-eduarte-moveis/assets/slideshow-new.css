/* MEDYA Eduarte Móveis - Slideshow Styles (Refatorado) */

/* Container principal */
.medya-eduarte-slideshow {
  position: relative;
  width: 100%;
  height: 50vh;
  min-height: 300px;
  overflow: hidden;
  border-radius: 0;
  background: #f5f5f5;
}

.medya-eduarte-slideshow .swiper-wrapper {
  height: 100%;
}

/* Estado de loading */
.medya-eduarte-slideshow.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.medya-eduarte-slideshow.loading::before {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0073aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Slides básicos */
.medya-eduarte-slide {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Overlay sutil */
.medya-eduarte-slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

/* Conteúdo do slide */
.medya-eduarte-slide-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

/* Fade customizado - SIMPLIFICADO */
.medya-eduarte-slideshow.medya-custom-fade .swiper-wrapper {
  position: relative;
}

.medya-eduarte-slideshow.medya-custom-fade .swiper-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

/* Ken Burns Effect */
.medya-ken-burns-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: inherit;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  animation: kenBurnsZoom 10s ease-in-out infinite alternate;
}

@keyframes kenBurnsZoom {
  0% {
    transform: scale(1) translate(0, 0);
  }
  100% {
    transform: scale(1.1) translate(-2%, -2%);
  }
}

.medya-ken-burns-effect.ken-burns-active {
  animation-play-state: running;
}

/* Navegação */
.medya-eduarte-slideshow .swiper-button-next,
.medya-eduarte-slideshow .swiper-button-prev {
  color: white;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 44px;
  height: 44px;
}

.medya-eduarte-slideshow .swiper-button-next:hover,
.medya-eduarte-slideshow .swiper-button-prev:hover {
  background: rgba(0, 0, 0, 0.5);
}

.medya-eduarte-slideshow .swiper-button-next::after,
.medya-eduarte-slideshow .swiper-button-prev::after {
  font-size: 18px;
}

/* Paginação */
.medya-eduarte-slideshow .swiper-pagination-bullet {
  background: white;
  opacity: 0.5;
}

.medya-eduarte-slideshow .swiper-pagination-bullet-active {
  opacity: 1;
}

/* Error State */
.medya-eduarte-slideshow.error {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 16px;
}

.medya-eduarte-slideshow.error::after {
  content: "Erro ao carregar slideshow";
}

/* Responsivo */
@media (max-width: 768px) {
  .medya-eduarte-slideshow {
    height: 40vh;
    min-height: 250px;
  }

  .medya-eduarte-slideshow .swiper-button-next,
  .medya-eduarte-slideshow .swiper-button-prev {
    width: 36px;
    height: 36px;
  }

  .medya-eduarte-slideshow .swiper-button-next::after,
  .medya-eduarte-slideshow .swiper-button-prev::after {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .medya-eduarte-slideshow {
    height: 30vh;
    min-height: 200px;
  }
}
