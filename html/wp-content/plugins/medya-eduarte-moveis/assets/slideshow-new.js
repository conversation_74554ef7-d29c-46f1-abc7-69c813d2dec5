/**
 * MEDYA Eduarte Móveis - Slideshow Widget (Refatorado)
 */
(function ($) {
    'use strict';

    class MedyaEduarteSlideshowHandler extends elementorModules.frontend.handlers.Base {
        getDefaultSettings() {
            return {
                selectors: {
                    slideshow: '.medya-eduarte-slideshow'
                }
            };
        }

        getDefaultElements() {
            const selectors = this.getSettings('selectors');
            return {
                $slideshow: this.$element.find(selectors.slideshow)
            };
        }

        bindEvents() {
            this.initSlideshow();
        }

        async initSlideshow() {
            const $slideshow = this.elements.$slideshow;

            if (!$slideshow.length) {
                console.warn('MEDYA Slideshow: Element not found');
                return;
            }

            try {
                // Obtém configurações
                const settings = JSON.parse($slideshow.attr('data-settings') || '{}');
                console.log('MEDYA Slideshow: Initializing with settings:', settings);

                // Aguarda Swiper estar disponível
                if (!elementorFrontend?.utils?.swiper) {
                    console.warn('MEDYA Slideshow: Swiper not available, retrying...');
                    setTimeout(() => this.initSlideshow(), 500);
                    return;
                }

                // Configuração simplificada do Swiper
                const swiperConfig = this.getSwiperConfig(settings);
                
                // Inicializa Swiper
                const Swiper = elementorFrontend.utils.swiper;
                this.swiper = await new Swiper($slideshow[0], swiperConfig);

                // Salva referência
                $slideshow.data('swiper', this.swiper);

                // Configurações pós-inicialização
                this.setupPostInit(settings);

                console.log('MEDYA Slideshow: Successfully initialized');

            } catch (error) {
                console.error('MEDYA Slideshow: Initialization error:', error);
                $slideshow.removeClass('loading').addClass('error');
            }
        }

        getSwiperConfig(settings) {
            const $slideshow = this.elements.$slideshow;

            const config = {
                // Configurações básicas
                loop: settings.loop !== false,
                speed: settings.speed || 500,
                effect: 'slide', // Sempre usa slide para evitar problemas
                spaceBetween: 0,
                allowTouchMove: true,
                grabCursor: true,
                watchSlidesProgress: true,
                
                // Eventos essenciais
                on: {
                    init: (swiper) => {
                        $slideshow.removeClass('loading');
                        console.log('MEDYA Slideshow: Swiper initialized');
                        
                        // Aplica fade customizado se necessário
                        if (settings.effect === 'fade') {
                            this.applyCustomFade();
                        }
                        
                        this.handleKenBurns(settings);
                    },
                    
                    slideChange: (swiper) => {
                        console.log('MEDYA Slideshow: Slide changed to:', swiper.activeIndex);
                        
                        if (settings.effect === 'fade') {
                            this.updateCustomFade();
                        }
                        
                        this.handleKenBurns(settings);
                    }
                }
            };

            // Configuração de autoplay simplificada
            if (settings.autoplay !== false) {
                config.autoplay = {
                    delay: settings.duration || 5000,
                    disableOnInteraction: false
                };
                console.log('MEDYA Slideshow: Autoplay enabled with delay:', config.autoplay.delay);
            }

            return config;
        }

        applyCustomFade() {
            const $slideshow = this.elements.$slideshow;
            $slideshow.addClass('medya-custom-fade');
            
            // Configura slides para fade
            const $slides = $slideshow.find('.swiper-slide');
            $slides.css({
                'position': 'absolute',
                'top': '0',
                'left': '0',
                'width': '100%',
                'height': '100%',
                'opacity': '0',
                'transition': 'opacity 0.5s ease-in-out'
            });
            
            // Mostra o primeiro slide
            $slides.first().css('opacity', '1');
            
            console.log('MEDYA Slideshow: Custom fade applied');
        }

        updateCustomFade() {
            if (!this.elements.$slideshow.hasClass('medya-custom-fade')) return;
            
            const $slides = this.elements.$slideshow.find('.swiper-slide');
            const activeIndex = this.swiper.activeIndex;
            
            // Esconde todos os slides
            $slides.css('opacity', '0');
            
            // Mostra apenas o slide ativo
            $slides.eq(activeIndex).css('opacity', '1');
            
            console.log('MEDYA Slideshow: Custom fade updated for slide:', activeIndex);
        }

        setupPostInit(settings) {
            // Ken Burns inicial
            setTimeout(() => {
                this.handleKenBurns(settings);
            }, 100);

            // Monitor de autoplay simplificado
            if (settings.autoplay !== false && this.swiper?.autoplay) {
                this.setupAutoplayMonitor(settings);
            }
        }

        setupAutoplayMonitor(settings) {
            let lastIndex = this.swiper.activeIndex;
            let stuckCount = 0;
            
            this.autoplayMonitor = setInterval(() => {
                if (!this.swiper?.autoplay) return;
                
                const currentIndex = this.swiper.activeIndex;
                const isRunning = this.swiper.autoplay.running;
                
                if (currentIndex === lastIndex && !isRunning) {
                    stuckCount++;
                    console.log('MEDYA Slideshow: Autoplay stuck, attempt:', stuckCount);
                    
                    if (stuckCount >= 2) {
                        console.log('MEDYA Slideshow: Restarting autoplay');
                        this.swiper.autoplay.start();
                        stuckCount = 0;
                    }
                } else {
                    stuckCount = 0;
                }
                
                lastIndex = currentIndex;
            }, (settings.duration || 5000) + 1000);
        }

        handleKenBurns(settings) {
            if (!settings.kenBurns) return;

            const $activeSlide = this.elements.$slideshow.find('.swiper-slide-active');
            const $kenBurnsElement = $activeSlide.find('.medya-ken-burns-effect');

            if ($kenBurnsElement.length) {
                $kenBurnsElement.removeClass('ken-burns-active');
                
                // Força reflow
                $kenBurnsElement[0].offsetHeight;
                
                $kenBurnsElement.addClass('ken-burns-active');
            }
        }

        onDestroy() {
            if (this.swiper) {
                this.swiper.destroy(true, true);
                this.swiper = null;
            }

            if (this.autoplayMonitor) {
                clearInterval(this.autoplayMonitor);
                this.autoplayMonitor = null;
            }
        }
    }

    // Registra o handler
    $(window).on('elementor/frontend/init', () => {
        elementorFrontend.hooks.addAction('frontend/element_ready/medya-eduarte-slideshow.default', ($scope) => {
            new MedyaEduarteSlideshowHandler({ $element: $scope });
        });
    });

})(jQuery);
