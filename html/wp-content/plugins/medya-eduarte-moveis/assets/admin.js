/* MEDYA Eduarte Móveis - Admin JavaScript */

(function ($) {
    'use strict';

    let sectionsData = [];
    let sectionCounter = 0;

    $(document).ready(function () {
        initSections();
        bindEvents();
    });

    function initSections() {
        // Carrega dados existentes
        const existingData = $('#sections-data').val();
        if (existingData && existingData !== '[]' && existingData !== '') {
            try {
                sectionsData = JSON.parse(existingData);
                if (sectionsData.length > 0) {
                    sectionCounter = Math.max(...sectionsData.map(s => parseInt(s.id.replace('section_', ''))), 0);
                } else {
                    sectionCounter = 0;
                }
            } catch (e) {
                sectionsData = [];
                sectionCounter = 0;
            }
        } else {
            // Se não há dados, sincroniza com o que está no DOM
            sectionsData = [];
            sectionCounter = 0;
            syncWithExistingSections();
        }

        // Inicializa sortable
        initSortable();
    }

    function bindEvents() {
        // Adicionar nova seção
        $('#add-section-btn').on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            addNewSection();
            return false;
        });

        $('#new-section-name').on('keypress', function (e) {
            if (e.which === 13) { // Enter
                e.preventDefault();
                e.stopPropagation();
                addNewSection();
                return false;
            }
        });

        // Deletar seção
        $(document).on('click', '.section-delete-btn', deleteSection);

        // Mudança no select de post
        $(document).on('change', '.section-post-select', updateSectionPost);

        // Mudança no tipo de post
        $('#post_type_select').on('change', updatePostTypeOptions);

        // Antes de submeter o form
        $('form').on('submit', function (e) {
            console.log('Formulário sendo submetido. Dados atuais:', sectionsData);
            updateSectionsData();

            // Verifica se os dados foram atualizados corretamente
            const hiddenFieldValue = $('#sections-data').val();
            console.log('Valor do campo hidden no submit:', hiddenFieldValue);

            // Força atualização se necessário
            if (!hiddenFieldValue || hiddenFieldValue === '[]') {
                $('#sections-data').val(JSON.stringify(sectionsData));
                console.log('Campo hidden atualizado forçadamente');
            }

            // Permite o submit normal do formulário
        });
    }

    function initSortable() {
        $('.sections-sortable').sortable({
            handle: '.section-drag-handle',
            placeholder: 'ui-sortable-placeholder',
            tolerance: 'pointer',
            update: function () {
                updateSectionOrder();
            }
        });
    }

    function addNewSection() {
        const nameInput = $('#new-section-name');
        const name = nameInput.val().trim();

        if (!name) {
            alert(medyaEduarte.strings.sectionName);
            nameInput.focus();
            return;
        }

        // Verifica se já existe (tanto no array local quanto no DOM)
        const nameExists = sectionsData.some(s => s.name.toLowerCase() === name.toLowerCase()) ||
            $('.section-name').toArray().some(el => $(el).text().trim().toLowerCase() === name.toLowerCase());

        if (nameExists) {
            alert('Uma seção com este nome já existe.');
            nameInput.focus();
            return;
        }

        sectionCounter++;
        const sectionId = 'section_' + sectionCounter;
        const newSection = {
            id: sectionId,
            name: name,
            post_id: '',
            order: sectionsData.length + 1
        };

        sectionsData.push(newSection);

        // Remove mensagem "nenhuma seção"
        $('.no-sections').remove();

        // Adiciona o HTML da nova seção
        const sectionHtml = createSectionHtml(newSection);

        if ($('.sections-sortable').length === 0) {
            $('#sections-list').html('<div class="sections-sortable"></div>');
        }

        $('.sections-sortable').append(sectionHtml);

        // Carrega posts para a nova seção
        loadPostsForNewSection(sectionId);

        // Reinicializa sortable
        initSortable();

        // Limpa o input
        nameInput.val('');

        // Atualiza dados e salva via AJAX
        updateSectionsData();
        saveSectionsViaAjax();
    }

    function createSectionHtml(section) {
        const postType = $('#post_type_select').val();

        return `
            <div class="section-item" data-section-id="${section.id}">
                <div class="section-header">
                    <span class="section-drag-handle">⋮⋮</span>
                    <span class="section-name">${escapeHtml(section.name)}</span>
                    <button type="button" class="section-delete-btn" title="${medyaEduarte.strings.confirmDelete}">×</button>
                </div>
                <div class="section-content">
                    <label>${medyaEduarte.strings.selectPost}:</label>
                    <select class="section-post-select" data-section-id="${section.id}">
                        <option value="">${medyaEduarte.strings.selectPost}</option>
                    </select>
                </div>
            </div>
        `;
    }

    function deleteSection() {
        if (!confirm(medyaEduarte.strings.confirmDelete)) {
            return;
        }

        const sectionItem = $(this).closest('.section-item');
        const sectionId = sectionItem.data('section-id');

        // Remove dos dados
        sectionsData = sectionsData.filter(s => s.id !== sectionId);

        // Remove do DOM
        sectionItem.remove();

        // Se não há mais seções, mostra mensagem
        if (sectionsData.length === 0) {
            $('#sections-list').html('<p class="no-sections">Nenhuma seção configurada ainda.</p>');
        }

        // Atualiza ordem
        updateSectionOrder();
    }

    function updateSectionPost() {
        const select = $(this);
        const sectionId = select.data('section-id');
        const postId = select.val();

        console.log('Atualizando post da seção:', sectionId, 'para post:', postId);

        // Atualiza nos dados
        const section = sectionsData.find(s => s.id === sectionId);
        if (section) {
            section.post_id = postId;
            updateSectionsData();
            saveSectionsViaAjax();
            console.log('Seção atualizada:', section);
        } else {
            console.warn('Seção não encontrada no array:', sectionId);
        }
    }

    function updateSectionOrder() {
        $('.section-item').each(function (index) {
            const sectionId = $(this).data('section-id');
            const section = sectionsData.find(s => s.id === sectionId);
            if (section) {
                section.order = index + 1;
            }
        });
        updateSectionsData();
        saveSectionsViaAjax();
    }

    function updatePostTypeOptions() {
        const postType = $(this).val();

        // Mostra loading
        $('.section-post-select').prop('disabled', true).html(`<option>${medyaEduarte.strings.loading}</option>`);

        // Busca posts do novo tipo
        $.ajax({
            url: medyaEduarte.ajaxUrl,
            type: 'POST',
            data: {
                action: 'medya_eduarte_get_posts',
                post_type: postType,
                nonce: medyaEduarte.nonce
            },
            success: function (response) {
                if (response.success) {
                    updatePostSelects(response.data);
                } else {
                    alert(medyaEduarte.strings.error);
                }
            },
            error: function () {
                alert(medyaEduarte.strings.error);
            },
            complete: function () {
                $('.section-post-select').prop('disabled', false);
            }
        });
    }

    function updatePostSelects(posts) {
        $('.section-post-select').each(function () {
            const select = $(this);
            const currentValue = select.val();

            let html = `<option value="">${medyaEduarte.strings.selectPost}</option>`;
            posts.forEach(function (post) {
                const selected = currentValue == post.value ? 'selected' : '';
                html += `<option value="${post.value}" ${selected}>${escapeHtml(post.label)}</option>`;
            });

            select.html(html);
        });
    }

    function syncWithExistingSections() {
        // Sincroniza com seções que já existem no DOM (após reload da página)
        $('.section-item').each(function (index) {
            const sectionId = $(this).data('section-id');
            const sectionName = $(this).find('.section-name').text().trim();
            const selectedPostId = $(this).find('.section-post-select').val() || '';

            if (sectionId && sectionName) {
                sectionsData.push({
                    id: sectionId,
                    name: sectionName,
                    post_id: selectedPostId,
                    order: index + 1
                });

                // Atualiza o contador
                const idNumber = parseInt(sectionId.replace('section_', ''));
                if (idNumber > sectionCounter) {
                    sectionCounter = idNumber;
                }
            }
        });

        // Atualiza o campo hidden
        updateSectionsData();
    }

    function loadPostsForNewSection(sectionId) {
        const postType = $('#post_type_select').val();
        const sectionSelect = $(`.section-post-select[data-section-id="${sectionId}"]`);

        if (!postType) {
            return;
        }

        // Mostra loading
        sectionSelect.prop('disabled', true).html(`<option>${medyaEduarte.strings.loading}</option>`);

        // Busca posts
        $.ajax({
            url: medyaEduarte.ajaxUrl,
            type: 'POST',
            data: {
                action: 'medya_eduarte_get_posts',
                post_type: postType,
                nonce: medyaEduarte.nonce
            },
            success: function (response) {
                if (response.success) {
                    let html = `<option value="">${medyaEduarte.strings.selectPost}</option>`;
                    response.data.forEach(function (post) {
                        html += `<option value="${post.value}">${escapeHtml(post.label)}</option>`;
                    });
                    sectionSelect.html(html);
                } else {
                    sectionSelect.html(`<option value="">${medyaEduarte.strings.error}</option>`);
                }
            },
            error: function () {
                sectionSelect.html(`<option value="">${medyaEduarte.strings.error}</option>`);
            },
            complete: function () {
                sectionSelect.prop('disabled', false);
            }
        });
    }

    function updateSectionsData() {
        const jsonData = JSON.stringify(sectionsData);
        $('#sections-data').val(jsonData);
        console.log('Campo hidden atualizado com:', jsonData);
        console.log('Valor atual do campo:', $('#sections-data').val());
    }

    function saveSectionsViaAjax() {
        console.log('Salvando seções via AJAX:', sectionsData);

        // Mostra indicador de salvamento
        showSaveIndicator('saving');

        $.ajax({
            url: medyaEduarte.ajaxUrl,
            type: 'POST',
            data: {
                action: 'medya_eduarte_save_sections',
                sections: JSON.stringify(sectionsData),
                nonce: medyaEduarte.nonce
            },
            success: function (response) {
                if (response.success) {
                    console.log('Seções salvas com sucesso');
                    showSaveIndicator('success');
                } else {
                    console.error('Erro ao salvar seções:', response);
                    showSaveIndicator('error');
                }
            },
            error: function (xhr, status, error) {
                console.error('Erro AJAX ao salvar seções:', error);
                showSaveIndicator('error');
            }
        });
    }

    function showSaveIndicator(status) {
        // Remove indicador anterior
        $('.save-indicator').remove();

        let message = '';
        let className = '';

        switch (status) {
            case 'saving':
                message = 'Salvando...';
                className = 'notice notice-info';
                break;
            case 'success':
                message = 'Salvo com sucesso!';
                className = 'notice notice-success';
                break;
            case 'error':
                message = 'Erro ao salvar';
                className = 'notice notice-error';
                break;
        }

        if (message) {
            const indicator = $(`<div class="save-indicator ${className} is-dismissible"><p>${message}</p></div>`);
            $('#sections-container').prepend(indicator);

            // Remove automaticamente após 3 segundos (exceto para saving)
            if (status !== 'saving') {
                setTimeout(() => {
                    indicator.fadeOut(() => indicator.remove());
                }, 3000);
            }
        }
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

})(jQuery);
