/* MEDYA Eduarte Móveis - Admin Styles */

#sections-container {
  margin-top: 20px;
}

.no-sections {
  padding: 20px;
  background: #f9f9f9;
  border: 2px dashed #ddd;
  text-align: center;
  color: #666;
  font-style: italic;
}

.sections-sortable {
  margin-bottom: 20px;
}

.section-item {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.section-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.section-item.ui-sortable-helper {
  transform: rotate(2deg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.section-header {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  cursor: move;
}

.section-drag-handle {
  color: #999;
  margin-right: 10px;
  font-size: 14px;
  cursor: grab;
}

.section-drag-handle:active {
  cursor: grabbing;
}

.section-name {
  flex: 1;
  font-weight: 600;
  color: #333;
}

.section-delete-btn {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.section-delete-btn:hover {
  background: #dc3545;
  color: white;
}

.section-content {
  padding: 15px;
}

.section-content label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #555;
}

.section-post-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 14px;
}

.section-post-select:focus {
  border-color: #0073aa;
  box-shadow: 0 0 0 1px #0073aa;
  outline: none;
}

.section-add-container {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border: 2px dashed #ddd;
  border-radius: 4px;
  margin-bottom: 20px;
}

#new-section-name {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

#new-section-name:focus {
  border-color: #0073aa;
  box-shadow: 0 0 0 1px #0073aa;
  outline: none;
}

#add-section-btn {
  padding: 10px 20px;
  background: #0073aa;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.2s ease;
}

#add-section-btn:hover {
  background: #005a87;
}

#add-section-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Sortable placeholder */
.ui-sortable-placeholder {
  height: 60px;
  background: #e3f2fd;
  border: 2px dashed #2196f3;
  border-radius: 4px;
  margin-bottom: 10px;
  visibility: visible !important;
}

/* Loading states */
.section-loading {
  opacity: 0.6;
  pointer-events: none;
}

.section-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0073aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Save Indicator */
.save-indicator {
  margin-bottom: 15px;
  animation: slideDown 0.3s ease-out;
}

.save-indicator.notice-info {
  border-left-color: #0073aa;
}

.save-indicator.notice-success {
  border-left-color: #46b450;
}

.save-indicator.notice-error {
  border-left-color: #dc3232;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .section-add-container {
    flex-direction: column;
    align-items: stretch;
  }

  #add-section-btn {
    width: 100%;
  }

  .section-header {
    padding: 10px 12px;
  }

  .section-content {
    padding: 12px;
  }
}
