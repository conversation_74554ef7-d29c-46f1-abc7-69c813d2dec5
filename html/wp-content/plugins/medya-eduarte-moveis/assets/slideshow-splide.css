/* MEDYA Eduarte Móveis - Slideshow com Splide */

/* Container principal */
.medya-eduarte-slideshow {
  position: relative;
  width: 100%;
  height: 50vh;
  min-height: 300px;
  overflow: hidden;
  border-radius: 0;
  background: #f5f5f5;
}

/* Estado de loading */
.medya-eduarte-slideshow.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.medya-eduarte-slideshow.loading::before {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0073aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Splide customizations */
.medya-eduarte-slideshow.splide {
  height: 100%;
}

.medya-eduarte-slideshow .splide__track {
  height: 100%;
}

.medya-eduarte-slideshow .splide__list {
  height: 100%;
  margin: 0;
  padding: 0;
}

.medya-eduarte-slideshow .splide__slide {
  height: 100%;
  list-style: none;
}

/* Slides básicos */
.medya-eduarte-slide {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Overlay sutil */
.medya-eduarte-slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

/* Conteúdo do slide */
.medya-eduarte-slide-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

/* Ken Burns Effect */
.medya-ken-burns-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: inherit;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  transition: transform 8s ease-in-out;
}

.medya-ken-burns-effect.ken-burns-active {
  transform: scale(1.1) translate(-2%, -2%);
}

/* Fade effect customization */
.medya-eduarte-slideshow.splide--fade .splide__slide {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.medya-eduarte-slideshow.splide--fade .splide__slide.is-active {
  opacity: 1;
}

/* Navigation customization */
.medya-eduarte-slideshow .splide__arrows {
  display: none; /* Escondemos por padrão, pode ser habilitado via configuração */
}

.medya-eduarte-slideshow .splide__arrow {
  background: rgba(0, 0, 0, 0.3);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  color: white;
  transition: background 0.3s ease;
}

.medya-eduarte-slideshow .splide__arrow:hover {
  background: rgba(0, 0, 0, 0.5);
}

.medya-eduarte-slideshow .splide__arrow svg {
  width: 18px;
  height: 18px;
}

/* Pagination customization */
.medya-eduarte-slideshow .splide__pagination {
  display: none; /* Escondemos por padrão, pode ser habilitado via configuração */
  bottom: 20px;
}

.medya-eduarte-slideshow .splide__pagination__page {
  background: rgba(255, 255, 255, 0.5);
  border: none;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  margin: 0 4px;
  transition: background 0.3s ease;
}

.medya-eduarte-slideshow .splide__pagination__page.is-active {
  background: white;
}

/* Error State */
.medya-eduarte-slideshow.error {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 16px;
}

.medya-eduarte-slideshow.error::after {
  content: "Erro ao carregar slideshow";
}

/* Responsivo */
@media (max-width: 768px) {
  .medya-eduarte-slideshow {
    height: 40vh;
    min-height: 250px;
  }

  .medya-eduarte-slideshow .splide__arrow {
    width: 36px;
    height: 36px;
  }

  .medya-eduarte-slideshow .splide__arrow svg {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .medya-eduarte-slideshow {
    height: 30vh;
    min-height: 200px;
  }
}

/* Garantir que slides sejam visíveis */
.medya-eduarte-slideshow .splide__slide {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  position: relative !important;
}

/* Para fade effect */
.medya-eduarte-slideshow.splide--fade .splide__slide {
  opacity: 0 !important;
  transition: opacity 0.5s ease-in-out !important;
}

.medya-eduarte-slideshow.splide--fade .splide__slide.is-active,
.medya-eduarte-slideshow.splide--fade .splide__slide.is-visible {
  opacity: 1 !important;
  z-index: 2 !important;
}

/* Força visibilidade do slide ativo independente do aria-hidden */
.medya-eduarte-slideshow .splide__slide.is-active[aria-hidden="true"] {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}
