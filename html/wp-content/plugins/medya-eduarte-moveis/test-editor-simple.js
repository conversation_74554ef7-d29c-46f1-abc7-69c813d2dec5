/* Teste Simples para Editor - MEDYA Eduarte Móveis */

console.log('🎯 TESTE SIMPLES DO EDITOR');

// Função para forçar inicialização no editor
function forceEditorInit() {
    console.log('🔧 Forçando inicialização no editor...');
    
    const slideshows = document.querySelectorAll('.medya-eduarte-slideshow');
    console.log('Slideshows encontrados:', slideshows.length);
    
    slideshows.forEach((slideshow, index) => {
        console.log(`\n📍 Slideshow ${index + 1}:`);
        
        // Verifica estrutura
        const track = slideshow.querySelector('.splide__track');
        const list = slideshow.querySelector('.splide__list');
        const slides = slideshow.querySelectorAll('.splide__slide');
        
        console.log('  - Track:', track ? '✅' : '❌');
        console.log('  - List:', list ? '✅' : '❌');
        console.log('  - Slides:', slides.length);
        
        if (slides.length === 0) {
            console.log('  ❌ Nenhum slide encontrado');
            return;
        }
        
        // Força visibilidade do primeiro slide
        const firstSlide = slides[0];
        firstSlide.style.setProperty('opacity', '1', 'important');
        firstSlide.style.setProperty('visibility', 'visible', 'important');
        firstSlide.style.setProperty('display', 'block', 'important');
        firstSlide.style.setProperty('position', 'relative', 'important');
        
        console.log('  ✅ Primeiro slide forçado a ficar visível');
        
        // Verifica se tem background-image
        const bgImage = firstSlide.style.backgroundImage;
        console.log('  - Background image:', bgImage ? '✅ Presente' : '❌ Ausente');
        
        if (!bgImage || bgImage === 'none') {
            // Tenta pegar do Ken Burns
            const kenBurns = firstSlide.querySelector('.medya-ken-burns-effect');
            if (kenBurns && kenBurns.style.backgroundImage) {
                firstSlide.style.backgroundImage = kenBurns.style.backgroundImage;
                console.log('  ✅ Background copiado do Ken Burns');
            }
        }
        
        // Tenta inicializar Splide se disponível
        if (typeof Splide !== 'undefined') {
            try {
                console.log('  🔧 Tentando inicializar Splide...');
                
                const splideOptions = {
                    type: 'loop',
                    autoplay: true,
                    interval: 5000,
                    arrows: false,
                    pagination: false,
                    drag: false
                };
                
                const splideInstance = new Splide(slideshow, splideOptions);
                splideInstance.mount();
                
                console.log('  ✅ Splide inicializado');
                
            } catch (error) {
                console.log('  ❌ Erro no Splide:', error.message);
            }
        } else {
            console.log('  ⚠️ Splide não disponível');
        }
    });
}

// Função para carregar Splide via CDN
function loadSplide() {
    console.log('📦 Carregando Splide via CDN...');
    
    // CSS
    if (!document.querySelector('link[href*="splide"]')) {
        const css = document.createElement('link');
        css.rel = 'stylesheet';
        css.href = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css';
        document.head.appendChild(css);
        console.log('  ✅ CSS do Splide carregado');
    }
    
    // JS
    if (typeof Splide === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js';
        script.onload = () => {
            console.log('  ✅ JS do Splide carregado');
            setTimeout(forceEditorInit, 500);
        };
        document.head.appendChild(script);
    } else {
        console.log('  ✅ Splide já disponível');
        forceEditorInit();
    }
}

// Função para verificar estado atual
function checkCurrentState() {
    console.log('🔍 VERIFICANDO ESTADO ATUAL:');
    
    console.log('  - Splide disponível:', typeof Splide !== 'undefined' ? '✅' : '❌');
    console.log('  - jQuery disponível:', typeof jQuery !== 'undefined' ? '✅' : '❌');
    console.log('  - No editor:', document.body.classList.contains('elementor-editor-active') ? '✅' : '❌');
    
    const slideshows = document.querySelectorAll('.medya-eduarte-slideshow');
    console.log('  - Slideshows:', slideshows.length);
    
    slideshows.forEach((slideshow, index) => {
        const slides = slideshow.querySelectorAll('.splide__slide');
        const visibleSlides = Array.from(slides).filter(slide => {
            const style = window.getComputedStyle(slide);
            return style.opacity > 0 && style.visibility !== 'hidden';
        });
        
        console.log(`    * Slideshow ${index + 1}: ${slides.length} slides, ${visibleSlides.length} visíveis`);
    });
}

// Executa verificação inicial
checkCurrentState();

// Tenta carregar Splide e inicializar
setTimeout(() => {
    loadSplide();
}, 1000);

// Expõe funções globalmente
window.forceEditorInit = forceEditorInit;
window.loadSplide = loadSplide;
window.checkCurrentState = checkCurrentState;

console.log('✅ Funções disponíveis:');
console.log('  - forceEditorInit() - Força inicialização');
console.log('  - loadSplide() - Carrega Splide via CDN');
console.log('  - checkCurrentState() - Verifica estado atual');

// Para usar:
// 1. Cole este código no console do editor
// 2. As funções executam automaticamente
// 3. Use as funções manuais se necessário
