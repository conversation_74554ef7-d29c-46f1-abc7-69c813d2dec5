/* Correção Imediata do Splide - MEDYA Eduarte Móveis */

(function() {
    console.log('🚨 CORREÇÃO IMEDIATA DO SPLIDE');
    
    function fixSplideVisibilityNow() {
        const slideshows = document.querySelectorAll('.medya-eduarte-slideshow.splide');
        
        slideshows.forEach((slideshow, index) => {
            console.log(`🔧 Corrigindo slideshow ${index + 1}:`);
            
            // 1. Remove aria-hidden de todos os slides ativos
            const activeSlides = slideshow.querySelectorAll('.splide__slide.is-active, .splide__slide.is-visible');
            activeSlides.forEach(slide => {
                slide.removeAttribute('aria-hidden');
                console.log('✅ Removido aria-hidden do slide ativo');
            });
            
            // 2. Força visibilidade via CSS inline
            activeSlides.forEach(slide => {
                slide.style.setProperty('opacity', '1', 'important');
                slide.style.setProperty('visibility', 'visible', 'important');
                slide.style.setProperty('display', 'block', 'important');
                slide.style.setProperty('position', 'relative', 'important');
                console.log('✅ Forçada visibilidade CSS do slide ativo');
            });
            
            // 3. Garante que o background-image esteja no slide principal
            activeSlides.forEach(slide => {
                const kenBurns = slide.querySelector('.medya-ken-burns-effect');
                if (kenBurns) {
                    const bgImage = kenBurns.style.backgroundImage;
                    if (bgImage && !slide.style.backgroundImage) {
                        slide.style.backgroundImage = bgImage;
                        console.log('✅ Background-image copiado para slide principal');
                    }
                }
            });
            
            // 4. Para fade effect, esconde slides não ativos
            if (slideshow.classList.contains('splide--fade')) {
                const inactiveSlides = slideshow.querySelectorAll('.splide__slide:not(.is-active):not(.is-visible)');
                inactiveSlides.forEach(slide => {
                    slide.style.setProperty('opacity', '0', 'important');
                });
                console.log(`✅ Escondidos ${inactiveSlides.length} slides inativos`);
            }
            
            // 5. Força altura do container
            slideshow.style.setProperty('height', '50vh', 'important');
            slideshow.style.setProperty('min-height', '300px', 'important');
            
            const track = slideshow.querySelector('.splide__track');
            if (track) {
                track.style.setProperty('height', '100%', 'important');
            }
            
            const list = slideshow.querySelector('.splide__list');
            if (list) {
                list.style.setProperty('height', '100%', 'important');
            }
            
            console.log(`✅ Slideshow ${index + 1} corrigido - ${activeSlides.length} slides ativos visíveis`);
        });
        
        return slideshows.length;
    }
    
    function testVisibility() {
        console.log('🧪 TESTE DE VISIBILIDADE:');
        
        const slideshows = document.querySelectorAll('.medya-eduarte-slideshow.splide');
        
        slideshows.forEach((slideshow, index) => {
            console.log(`\n📋 Slideshow ${index + 1}:`);
            
            const allSlides = slideshow.querySelectorAll('.splide__slide');
            const activeSlides = slideshow.querySelectorAll('.splide__slide.is-active, .splide__slide.is-visible');
            
            console.log(`  - Total slides: ${allSlides.length}`);
            console.log(`  - Slides ativos: ${activeSlides.length}`);
            
            activeSlides.forEach((slide, i) => {
                const computedStyle = window.getComputedStyle(slide);
                const opacity = computedStyle.opacity;
                const visibility = computedStyle.visibility;
                const display = computedStyle.display;
                const ariaHidden = slide.getAttribute('aria-hidden');
                const bgImage = slide.style.backgroundImage || computedStyle.backgroundImage;
                
                console.log(`  - Slide ativo ${i + 1}:`);
                console.log(`    * Opacity: ${opacity}`);
                console.log(`    * Visibility: ${visibility}`);
                console.log(`    * Display: ${display}`);
                console.log(`    * Aria-hidden: ${ariaHidden}`);
                console.log(`    * Background-image: ${bgImage ? '✅ Presente' : '❌ Ausente'}`);
                
                if (opacity === '1' && visibility === 'visible' && display !== 'none' && bgImage !== 'none') {
                    console.log(`    ✅ Slide ${i + 1} deve estar visível!`);
                } else {
                    console.log(`    ❌ Slide ${i + 1} tem problemas de visibilidade`);
                }
            });
        });
    }
    
    function forceBackgroundImages() {
        console.log('🖼️ FORÇANDO BACKGROUND IMAGES:');
        
        const slides = document.querySelectorAll('.medya-eduarte-slideshow .splide__slide');
        
        slides.forEach((slide, index) => {
            const kenBurns = slide.querySelector('.medya-ken-burns-effect');
            if (kenBurns) {
                const bgImage = kenBurns.style.backgroundImage;
                if (bgImage) {
                    slide.style.backgroundImage = bgImage;
                    slide.style.backgroundSize = 'cover';
                    slide.style.backgroundPosition = 'center';
                    slide.style.backgroundRepeat = 'no-repeat';
                    console.log(`✅ Background aplicado ao slide ${index + 1}`);
                }
            }
        });
    }
    
    // Executa correções
    const slideshowsFixed = fixSplideVisibilityNow();
    console.log(`✅ ${slideshowsFixed} slideshows corrigidos`);
    
    // Força background images
    forceBackgroundImages();
    
    // Testa visibilidade
    setTimeout(testVisibility, 500);
    
    // Expõe funções
    window.fixSplideVisibilityNow = fixSplideVisibilityNow;
    window.testVisibility = testVisibility;
    window.forceBackgroundImages = forceBackgroundImages;
    
    console.log('🛠️ Funções disponíveis:');
    console.log('  - fixSplideVisibilityNow() - Correção completa');
    console.log('  - testVisibility() - Teste detalhado');
    console.log('  - forceBackgroundImages() - Força background images');
    
})();

// Para usar:
// 1. Cole este código no console
// 2. As correções são aplicadas automaticamente
// 3. Use as funções para testes adicionais
