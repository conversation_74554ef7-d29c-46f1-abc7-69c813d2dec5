/* Correção Rápida de Visibilidade - MEDYA Eduarte Móveis */

(function() {
    console.log('🔧 CORREÇÃO DE VISIBILIDADE INICIADA');
    
    function fixSlideVisibility() {
        const slideshows = document.querySelectorAll('.medya-eduarte-slideshow');
        
        slideshows.forEach((slideshow, index) => {
            console.log(`🎯 Corrigindo slideshow ${index + 1}:`);
            
            // Esconde todos os slides
            const allSlides = slideshow.querySelectorAll('.swiper-slide');
            allSlides.forEach(slide => {
                slide.style.opacity = '0';
            });
            
            // Mostra apenas o slide ativo
            const activeSlides = slideshow.querySelectorAll('.swiper-slide-active');
            activeSlides.forEach(slide => {
                slide.style.opacity = '1';
                console.log('✅ Slide ativo tornado visível:', slide);
            });
            
            console.log(`📊 Total slides: ${allSlides.length}, Ativos: ${activeSlides.length}`);
        });
    }
    
    function monitorAndFix() {
        console.log('👁️ Iniciando monitoramento automático...');
        
        setInterval(() => {
            const slideshows = document.querySelectorAll('.medya-eduarte-slideshow.medya-custom-fade');
            
            slideshows.forEach(slideshow => {
                const activeSlides = slideshow.querySelectorAll('.swiper-slide-active');
                
                activeSlides.forEach(slide => {
                    if (slide.style.opacity !== '1') {
                        slide.style.opacity = '1';
                        console.log('🔄 Slide ativo corrigido automaticamente');
                    }
                });
                
                // Esconde slides não ativos
                const inactiveSlides = slideshow.querySelectorAll('.swiper-slide:not(.swiper-slide-active)');
                inactiveSlides.forEach(slide => {
                    if (slide.style.opacity !== '0') {
                        slide.style.opacity = '0';
                    }
                });
            });
        }, 500);
    }
    
    function testSlideVisibility() {
        console.log('🧪 TESTE DE VISIBILIDADE:');
        
        const slideshows = document.querySelectorAll('.medya-eduarte-slideshow');
        
        slideshows.forEach((slideshow, index) => {
            console.log(`\n📋 Slideshow ${index + 1}:`);
            
            const allSlides = slideshow.querySelectorAll('.swiper-slide');
            const activeSlides = slideshow.querySelectorAll('.swiper-slide-active');
            const visibleSlides = Array.from(allSlides).filter(slide => {
                const opacity = window.getComputedStyle(slide).opacity;
                return opacity === '1' || opacity > 0.5;
            });
            
            console.log(`  - Total slides: ${allSlides.length}`);
            console.log(`  - Slides ativos: ${activeSlides.length}`);
            console.log(`  - Slides visíveis: ${visibleSlides.length}`);
            
            if (activeSlides.length > 0 && visibleSlides.length === 0) {
                console.log('  ❌ PROBLEMA: Slides ativos mas nenhum visível!');
            } else if (visibleSlides.length > 0) {
                console.log('  ✅ OK: Slides visíveis encontrados');
            }
            
            // Mostra detalhes dos slides ativos
            activeSlides.forEach((slide, i) => {
                const opacity = window.getComputedStyle(slide).opacity;
                const inlineOpacity = slide.style.opacity;
                console.log(`  - Slide ativo ${i + 1}: opacity computed=${opacity}, inline=${inlineOpacity}`);
            });
        });
    }
    
    // Executa correção imediata
    fixSlideVisibility();
    
    // Executa teste
    setTimeout(testSlideVisibility, 1000);
    
    // Expõe funções globalmente
    window.fixSlideVisibility = fixSlideVisibility;
    window.monitorAndFix = monitorAndFix;
    window.testSlideVisibility = testSlideVisibility;
    
    console.log('✅ CORREÇÃO CONCLUÍDA');
    console.log('🛠️ Funções disponíveis:');
    console.log('  - fixSlideVisibility() - Corrige uma vez');
    console.log('  - monitorAndFix() - Monitora e corrige automaticamente');
    console.log('  - testSlideVisibility() - Testa estado atual');
    
})();

// Para usar:
// 1. Cole este código no console
// 2. Execute fixSlideVisibility() para correção imediata
// 3. Execute monitorAndFix() para correção automática contínua
// 4. Execute testSlideVisibility() para verificar estado
