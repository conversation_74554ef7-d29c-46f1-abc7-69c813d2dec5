<?php
/**
 * Teste específico para o campo ACF "galeria"
 * 
 * Execute este arquivo para diagnosticar o problema do campo galeria
 */

if (!defined('ABSPATH')) {
    require_once('../../../wp-load.php');
}

function test_acf_gallery_field() {
    echo "<h2>Teste do Campo ACF 'galeria'</h2>";
    
    // Post ID que está causando problema
    $post_id = 185; // Post "Banheiros"
    
    echo "<h3>Informações do Post</h3>";
    $post = get_post($post_id);
    if ($post) {
        echo "<p><strong>Post:</strong> " . esc_html($post->post_title) . "</p>";
        echo "<p><strong>Status:</strong> " . esc_html($post->post_status) . "</p>";
        echo "<p><strong>Tipo:</strong> " . esc_html($post->post_type) . "</p>";
    } else {
        echo "<p style='color: red;'>Post não encontrado!</p>";
        return;
    }
    
    echo "<h3>Teste do Campo 'galeria'</h3>";
    
    // Testa diferentes formas de obter o campo
    echo "<h4>1. get_field('galeria', $post_id)</h4>";
    $gallery1 = get_field('galeria', $post_id);
    echo "<p><strong>Tipo:</strong> " . gettype($gallery1) . "</p>";
    echo "<p><strong>Vazio:</strong> " . (empty($gallery1) ? 'Sim' : 'Não') . "</p>";
    echo "<pre>" . esc_html(print_r($gallery1, true)) . "</pre>";
    
    echo "<h4>2. get_field('galeria', $post_id, false) - Raw</h4>";
    $gallery2 = get_field('galeria', $post_id, false);
    echo "<p><strong>Tipo:</strong> " . gettype($gallery2) . "</p>";
    echo "<p><strong>Vazio:</strong> " . (empty($gallery2) ? 'Sim' : 'Não') . "</p>";
    echo "<pre>" . esc_html(print_r($gallery2, true)) . "</pre>";
    
    echo "<h4>3. get_post_meta('galeria')</h4>";
    $gallery3 = get_post_meta($post_id, 'galeria', true);
    echo "<p><strong>Tipo:</strong> " . gettype($gallery3) . "</p>";
    echo "<p><strong>Vazio:</strong> " . (empty($gallery3) ? 'Sim' : 'Não') . "</p>";
    echo "<pre>" . esc_html(print_r($gallery3, true)) . "</pre>";
    
    echo "<h4>4. Todos os meta fields do post</h4>";
    $all_meta = get_post_meta($post_id);
    echo "<p><strong>Campos que começam com 'galeria':</strong></p>";
    foreach ($all_meta as $key => $value) {
        if (strpos($key, 'galeria') !== false) {
            echo "<p><strong>$key:</strong></p>";
            echo "<pre>" . esc_html(print_r($value, true)) . "</pre>";
        }
    }
    
    echo "<h4>5. Teste da função process_acf_gallery</h4>";
    if (class_exists('Medya_Eduarte_Moveis')) {
        $processed = Medya_Eduarte_Moveis::process_acf_gallery($gallery1);
        echo "<p><strong>Resultado processado:</strong></p>";
        echo "<p><strong>Tipo:</strong> " . gettype($processed) . "</p>";
        echo "<p><strong>Count:</strong> " . (is_array($processed) ? count($processed) : 'N/A') . "</p>";
        echo "<pre>" . esc_html(print_r($processed, true)) . "</pre>";
    }
    
    echo "<h4>6. Informações do ACF</h4>";
    if (function_exists('get_field_object')) {
        $field_object = get_field_object('galeria', $post_id);
        if ($field_object) {
            echo "<p><strong>Configuração do campo:</strong></p>";
            echo "<pre>" . esc_html(print_r($field_object, true)) . "</pre>";
        } else {
            echo "<p style='color: red;'>Campo 'galeria' não encontrado no ACF!</p>";
        }
    } else {
        echo "<p style='color: red;'>ACF não está ativo!</p>";
    }
    
    echo "<h4>7. Teste manual de IDs de imagem</h4>";
    // Se o campo contém IDs, vamos testar manualmente
    if (is_string($gallery1) && is_numeric($gallery1)) {
        echo "<p>Campo parece ser um ID único: " . esc_html($gallery1) . "</p>";
        $image_url = wp_get_attachment_image_url($gallery1, 'full');
        if ($image_url) {
            echo "<p>✅ Imagem encontrada: <img src='" . esc_url($image_url) . "' style='max-width: 100px; height: auto;'></p>";
        } else {
            echo "<p>❌ Imagem não encontrada para ID: " . esc_html($gallery1) . "</p>";
        }
    } elseif (is_string($gallery1) && strpos($gallery1, ',') !== false) {
        echo "<p>Campo parece ser IDs separados por vírgula: " . esc_html($gallery1) . "</p>";
        $ids = explode(',', $gallery1);
        foreach ($ids as $id) {
            $id = trim($id);
            if (is_numeric($id)) {
                $image_url = wp_get_attachment_image_url($id, 'full');
                if ($image_url) {
                    echo "<p>✅ Imagem ID $id: <img src='" . esc_url($image_url) . "' style='max-width: 100px; height: auto;'></p>";
                } else {
                    echo "<p>❌ Imagem não encontrada para ID: $id</p>";
                }
            }
        }
    }
    
    echo "<h4>8. Busca por outros campos de galeria</h4>";
    $possible_fields = ['gallery', 'images', 'fotos', 'imagens', 'galeria_imagens'];
    foreach ($possible_fields as $field) {
        $value = get_field($field, $post_id);
        if (!empty($value)) {
            echo "<p><strong>Campo '$field' encontrado:</strong></p>";
            echo "<p><strong>Tipo:</strong> " . gettype($value) . "</p>";
            echo "<pre>" . esc_html(print_r($value, true)) . "</pre>";
        }
    }
}

// Se executado diretamente ou via URL
if (!defined('ABSPATH') || (isset($_GET['test_acf']) && $_GET['test_acf'] == '1')) {
    test_acf_gallery_field();
}

// Hook para usar via URL
add_action('init', function() {
    if (isset($_GET['test_acf']) && $_GET['test_acf'] == '1') {
        test_acf_gallery_field();
        exit;
    }
});

// Para usar:
// 1. Via URL: /?test_acf=1
// 2. Via WP-CLI: wp eval-file wp-content/plugins/medya-eduarte-moveis/test-acf-field.php
// 3. Via functions.php: include_once(WP_PLUGIN_DIR . '/medya-eduarte-moveis/test-acf-field.php'); e acesse /?test_acf=1
