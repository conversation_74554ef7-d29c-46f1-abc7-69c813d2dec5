/* Teste Simples - MEDYA Eduarte Móveis */

// Execute este código no console para testar rapidamente

(function() {
    console.log('🔍 TESTE SIMPLES INICIADO');
    
    // Encontra slideshows
    const slideshows = document.querySelectorAll('.medya-eduarte-slideshow');
    console.log('📊 Slideshows encontrados:', slideshows.length);
    
    if (slideshows.length === 0) {
        console.log('❌ Nenhum slideshow encontrado!');
        return;
    }
    
    slideshows.forEach((slideshow, index) => {
        console.log(`\n🎯 Testando slideshow ${index + 1}:`);
        
        // Verifica Swiper
        const swiper = slideshow.swiper || jQuery(slideshow).data('swiper');
        console.log('📱 Swiper instance:', swiper ? '✅ Encontrado' : '❌ Não encontrado');
        
        if (!swiper) {
            console.log('⚠️ Swiper não inicializado');
            return;
        }
        
        // Informações básicas
        console.log('📋 Informações básicas:');
        console.log('  - Slides total:', swiper.slides.length);
        console.log('  - Slide ativo:', swiper.activeIndex);
        console.log('  - Loop:', swiper.params.loop);
        console.log('  - Autoplay:', swiper.params.autoplay);
        
        // Status do autoplay
        if (swiper.autoplay) {
            console.log('🔄 Status do autoplay:');
            console.log('  - Running:', swiper.autoplay.running);
            console.log('  - Paused:', swiper.autoplay.paused);
            console.log('  - Delay:', swiper.params.autoplay.delay);
        } else {
            console.log('❌ Autoplay não configurado');
        }
        
        // Testa mudança manual
        console.log('🧪 Testando mudança manual...');
        const originalIndex = swiper.activeIndex;
        
        setTimeout(() => {
            const nextIndex = (originalIndex + 1) % swiper.slides.length;
            swiper.slideTo(nextIndex);
            console.log(`📍 Mudou para slide ${nextIndex}`);
            
            // Verifica se mudou
            setTimeout(() => {
                if (swiper.activeIndex === nextIndex) {
                    console.log('✅ Mudança manual funcionou');
                } else {
                    console.log('❌ Mudança manual falhou');
                }
            }, 100);
        }, 1000);
        
        // Testa restart do autoplay
        if (swiper.autoplay) {
            console.log('🔄 Testando restart do autoplay...');
            
            setTimeout(() => {
                swiper.autoplay.stop();
                console.log('⏹️ Autoplay parado');
                
                setTimeout(() => {
                    swiper.autoplay.start();
                    console.log('▶️ Autoplay reiniciado');
                    
                    setTimeout(() => {
                        console.log('📊 Status após restart:', swiper.autoplay.running ? '✅ Funcionando' : '❌ Parado');
                    }, 500);
                }, 1000);
            }, 2000);
        }
    });
    
    // Monitor simples
    console.log('\n👁️ Iniciando monitor simples (30 segundos)...');
    let monitorCount = 0;
    const monitorInterval = setInterval(() => {
        monitorCount++;
        
        slideshows.forEach((slideshow, index) => {
            const swiper = slideshow.swiper || jQuery(slideshow).data('swiper');
            if (swiper && swiper.autoplay) {
                console.log(`📊 Monitor ${monitorCount} - Slideshow ${index + 1}: Slide ${swiper.activeIndex}, Autoplay: ${swiper.autoplay.running ? '✅' : '❌'}`);
            }
        });
        
        if (monitorCount >= 6) { // 30 segundos
            clearInterval(monitorInterval);
            console.log('🏁 Monitor finalizado');
        }
    }, 5000);
    
    console.log('✅ TESTE SIMPLES CONFIGURADO');
})();

// Funções auxiliares
window.testSlideshow = function() {
    const slideshow = document.querySelector('.medya-eduarte-slideshow');
    const swiper = slideshow?.swiper || jQuery(slideshow).data('swiper');
    
    if (!swiper) {
        console.log('❌ Swiper não encontrado');
        return;
    }
    
    console.log('🧪 Teste rápido do slideshow:');
    console.log('Slide atual:', swiper.activeIndex);
    console.log('Autoplay:', swiper.autoplay?.running ? 'Funcionando' : 'Parado');
    
    // Força próximo slide
    swiper.slideNext();
    console.log('➡️ Forçou próximo slide');
};

window.restartAutoplay = function() {
    const slideshow = document.querySelector('.medya-eduarte-slideshow');
    const swiper = slideshow?.swiper || jQuery(slideshow).data('swiper');
    
    if (!swiper?.autoplay) {
        console.log('❌ Autoplay não disponível');
        return;
    }
    
    swiper.autoplay.stop();
    setTimeout(() => {
        swiper.autoplay.start();
        console.log('🔄 Autoplay reiniciado');
    }, 500);
};

console.log('🛠️ Funções disponíveis:');
console.log('- testSlideshow() - Teste rápido');
console.log('- restartAutoplay() - Reinicia autoplay');

// Para usar:
// 1. Cole este código no console
// 2. Aguarde os testes automáticos
// 3. Use testSlideshow() para teste manual
// 4. Use restartAutoplay() se necessário
