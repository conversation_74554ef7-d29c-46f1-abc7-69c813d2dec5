<?php
/*
 * Plugin Name: MEDYA Eduarte Móveis
 * Plugin URI: https://medya.com.br
 * Description: Plugin para gerenciar seções de galeria da home e integração com Elementor para exibição de slideshows.
 * Version: 1.0.0
 * Author: MEDYA Soluções Digitais
 * Author URI: https://medya.com.br/
 * Text Domain: medya-eduarte-moveis
 */

if (!defined('ABSPATH')) {
    exit; // Impede acesso direto.
}

// Define constantes para caminhos e URLs
if (!defined('MEDYA_EDUARTE_PATH')) {
    define('MEDYA_EDUARTE_PATH', plugin_dir_path(__FILE__));
}
if (!defined('MEDYA_EDUARTE_URL')) {
    define('MEDYA_EDUARTE_URL', plugin_dir_url(__FILE__));
}

// Carrega os arquivos dos módulos.
require_once MEDYA_EDUARTE_PATH . 'includes/class-admin.php';
require_once MEDYA_EDUARTE_PATH . 'includes/class-handler.php';

final class Medya_Eduarte_Moveis
{
    const VERSION = '1.0.0';
    const OPTION_SECTIONS = 'medya_eduarte_sections';
    const OPTION_POST_TYPE = 'medya_eduarte_post_type';
    const MENU_SLUG = 'medya-eduarte-moveis';

    private static $instance = null;
    private $missing_dependencies = [];

    public $admin;
    public $handler;

    public static function get_instance()
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct()
    {
        add_action('plugins_loaded', [$this, 'init_plugin']);

        // Hooks de ativação e desativação
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);
    }

    public function init_plugin()
    {
        // Instancia o módulo de administração
        $this->admin = new Medya_Eduarte_Moveis_Admin(
            self::VERSION,
            self::OPTION_SECTIONS,
            self::OPTION_POST_TYPE,
            self::MENU_SLUG
        );

        // Verifica se as dependências estão carregadas.
        if (!$this->check_dependencies()) {
            add_action('admin_notices', [$this, 'display_missing_dependencies_notice']);
            return;
        }

        // Instancia o handler que gerencia o widget do Elementor
        $this->handler = new Medya_Eduarte_Moveis_Handler(self::VERSION);
    }

    /**
     * Verifica se as dependências necessárias estão presentes.
     */
    private function check_dependencies()
    {
        $this->missing_dependencies = [];

        if (!did_action('medya_core/loaded')) {
            $this->missing_dependencies[] = 'MEDYA Core';
        }

        if (!did_action('elementor/loaded')) {
            $this->missing_dependencies[] = 'Elementor';
        }

        return empty($this->missing_dependencies);
    }

    public function display_missing_dependencies_notice()
    {
        if (empty($this->missing_dependencies)) {
            return;
        }

        $message = sprintf(
            esc_html__('O plugin "MEDYA Eduarte Móveis" requer os seguintes plugins: %s', 'medya-eduarte-moveis'),
            '<strong>' . implode(', ', $this->missing_dependencies) . '</strong>'
        );

        printf('<div class="notice notice-warning is-dismissible"><p>%s</p></div>', $message);
    }

    /**
     * Obtém as seções configuradas
     */
    public static function get_sections()
    {
        $sections = get_option(self::OPTION_SECTIONS, []);

        // Garante que sempre retorna um array
        if (!is_array($sections)) {
            return [];
        }

        // Se o array está vazio, retorna vazio
        if (empty($sections)) {
            return [];
        }

        // Ordena por order
        usort($sections, function ($a, $b) {
            return ($a['order'] ?? 0) - ($b['order'] ?? 0);
        });

        return $sections;
    }

    /**
     * Obtém o post type configurado
     */
    public static function get_post_type()
    {
        return get_option(self::OPTION_POST_TYPE, 'post');
    }

    /**
     * Obtém as imagens de uma seção específica
     */
    public static function get_section_images($section_id)
    {
        $sections = self::get_sections();

        foreach ($sections as $section) {
            if ($section['id'] === $section_id && !empty($section['post_id'])) {
                $gallery = get_field('galeria', $section['post_id']);

                // Debug para identificar o formato
                error_log('MEDYA Eduarte get_section_images - Post ID: ' . $section['post_id']);
                error_log('MEDYA Eduarte get_section_images - Gallery type: ' . gettype($gallery));
                error_log('MEDYA Eduarte get_section_images - Gallery content: ' . print_r($gallery, true));

                // Tenta diferentes formatos de dados do ACF
                $processed_gallery = self::process_acf_gallery($gallery);

                if (!empty($processed_gallery)) {
                    $images = [];
                    foreach ($processed_gallery as $image) {
                        if (is_array($image)) {
                            // Tenta diferentes formatos de URL do ACF
                            $image_url = '';
                            $image_id = 0;
                            $image_alt = '';
                            $image_title = '';

                            // Formato 1: ACF com full_image_url (seu caso)
                            if (isset($image['full_image_url'])) {
                                $image_url = $image['full_image_url'];
                                $image_id = $image['id'] ?? 0;
                                $image_alt = $image['alt_text'] ?? '';
                                $image_title = $image['title'] ?? '';
                            }
                            // Formato 2: ACF padrão com url
                            elseif (isset($image['url'])) {
                                $image_url = $image['url'];
                                $image_id = $image['id'] ?? 0;
                                $image_alt = $image['alt'] ?? '';
                                $image_title = $image['title'] ?? '';
                            }
                            // Formato 3: Apenas ID (busca dados da imagem)
                            elseif (is_numeric($image)) {
                                $image_id = intval($image);
                                $image_url = wp_get_attachment_image_url($image_id, 'full');
                                $image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
                                $image_title = get_the_title($image_id);
                            }
                            // Formato 4: Array com apenas ID
                            elseif (isset($image['ID'])) {
                                $image_id = intval($image['ID']);
                                $image_url = wp_get_attachment_image_url($image_id, 'full');
                                $image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
                                $image_title = get_the_title($image_id);
                            }

                            if (!empty($image_url)) {
                                $images[] = [
                                    'id' => $image_id,
                                    'url' => $image_url,
                                    'alt' => $image_alt ?: '',
                                    'title' => $image_title ?: ''
                                ];
                            }
                        }
                        // Se for apenas um ID numérico
                        elseif (is_numeric($image)) {
                            $image_id = intval($image);
                            $image_url = wp_get_attachment_image_url($image_id, 'full');

                            if ($image_url) {
                                $images[] = [
                                    'id' => $image_id,
                                    'url' => $image_url,
                                    'alt' => get_post_meta($image_id, '_wp_attachment_image_alt', true) ?: '',
                                    'title' => get_the_title($image_id) ?: ''
                                ];
                            }
                        }
                    }

                    return apply_filters('medya_eduarte_section_images', $images, $section_id);
                }
            }
        }

        return apply_filters('medya_eduarte_section_images', [], $section_id);
    }

    /**
     * Processa diferentes formatos de galeria do ACF
     */
    public static function process_acf_gallery($gallery)
    {
        if (empty($gallery)) {
            return [];
        }

        // Se já é um array, retorna como está
        if (is_array($gallery)) {
            return $gallery;
        }

        // Se é uma string, pode ser IDs separados por vírgula
        if (is_string($gallery)) {
            // Tenta decodificar JSON
            $json_decoded = json_decode($gallery, true);
            if (is_array($json_decoded)) {
                return $json_decoded;
            }

            // Tenta IDs separados por vírgula
            if (strpos($gallery, ',') !== false) {
                $ids = explode(',', $gallery);
                $processed = [];
                foreach ($ids as $id) {
                    $id = trim($id);
                    if (is_numeric($id)) {
                        $processed[] = intval($id);
                    }
                }
                return $processed;
            }

            // Se é um único ID
            if (is_numeric($gallery)) {
                return [intval($gallery)];
            }
        }

        // Se é um número (ID único)
        if (is_numeric($gallery)) {
            return [intval($gallery)];
        }

        // Se é um objeto, tenta converter para array
        if (is_object($gallery)) {
            return (array) $gallery;
        }

        error_log('MEDYA Eduarte: Formato de galeria não reconhecido: ' . print_r($gallery, true));
        return [];
    }

    /**
     * Obtém opções para select de seções
     */
    public static function get_sections_options()
    {
        $sections = self::get_sections();
        $options = ['' => esc_html__('Selecione uma seção', 'medya-eduarte-moveis')];

        foreach ($sections as $section) {
            $options[$section['id']] = $section['name'];
        }

        return apply_filters('medya_eduarte_sections_options', $options);
    }

    /**
     * Ativação do plugin
     */
    public function activate()
    {
        // Cria opções padrão se não existirem
        $existing_sections = get_option(self::OPTION_SECTIONS);
        if ($existing_sections === false) {
            add_option(self::OPTION_SECTIONS, []);
        } elseif (!is_array($existing_sections)) {
            // Corrige se a opção existe mas não é um array
            update_option(self::OPTION_SECTIONS, []);
        }

        $existing_post_type = get_option(self::OPTION_POST_TYPE);
        if ($existing_post_type === false) {
            add_option(self::OPTION_POST_TYPE, 'post');
        }

        // Flush rewrite rules se necessário
        flush_rewrite_rules();

        // Log de ativação
        error_log('MEDYA Eduarte Móveis: Plugin ativado');
    }

    /**
     * Desativação do plugin
     */
    public function deactivate()
    {
        // Flush rewrite rules
        flush_rewrite_rules();

        // Log de desativação
        error_log('MEDYA Eduarte Móveis: Plugin desativado');

        // Nota: Não removemos as opções para preservar dados
        // Em caso de desinstalação, isso seria feito em uninstall.php
    }
}

Medya_Eduarte_Moveis::get_instance();
