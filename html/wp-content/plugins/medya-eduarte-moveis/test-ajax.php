<?php
/**
 * Teste AJAX para MEDYA Eduarte Móveis
 * 
 * Adicione este código temporariamente ao functions.php para testar o AJAX
 */

if (!defined('ABSPATH')) {
    exit;
}

// Teste da função AJAX get_posts
add_action('wp_ajax_test_medya_eduarte_posts', function() {
    // Simula a mesma lógica do plugin
    $post_type = sanitize_text_field($_POST['post_type'] ?? 'post');
    
    echo "<h3>Teste AJAX - Buscar Posts</h3>";
    echo "<p><strong>Post Type:</strong> " . esc_html($post_type) . "</p>";
    
    $posts = get_posts([
        'post_type' => $post_type,
        'numberposts' => -1,
        'post_status' => 'publish'
    ]);
    
    echo "<p><strong>Posts encontrados:</strong> " . count($posts) . "</p>";
    
    if ($posts) {
        echo "<ul>";
        foreach ($posts as $post) {
            echo "<li>ID: {$post->ID} - {$post->post_title}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Nenhum post encontrado para este tipo.</p>";
    }
    
    // Testa também a função do plugin se estiver ativa
    if (class_exists('Medya_Eduarte_Moveis_Admin')) {
        echo "<h4>Teste da função do plugin:</h4>";
        
        $admin = new Medya_Eduarte_Moveis_Admin('1.0.0', 'medya_eduarte_sections', 'medya_eduarte_post_type', 'medya-eduarte-moveis');
        
        // Simula $_POST para o método ajax_get_posts
        $_POST['post_type'] = $post_type;
        $_POST['nonce'] = wp_create_nonce('medya_eduarte_nonce');
        
        ob_start();
        try {
            $admin->ajax_get_posts();
        } catch (Exception $e) {
            echo "Erro: " . $e->getMessage();
        }
        $output = ob_get_clean();
        
        echo "<pre>" . esc_html($output) . "</pre>";
    }
    
    wp_die();
});

// Adiciona botão de teste no admin
add_action('admin_notices', function() {
    if (isset($_GET['page']) && $_GET['page'] === 'medya-eduarte-moveis') {
        ?>
        <div class="notice notice-info">
            <p>
                <strong>Teste AJAX:</strong>
                <button type="button" onclick="testAjax('post')" class="button">Testar Posts</button>
                <button type="button" onclick="testAjax('page')" class="button">Testar Páginas</button>
                <button type="button" onclick="testAjax('galeria')" class="button">Testar Galerias</button>
            </p>
            <div id="ajax-test-result"></div>
        </div>
        
        <script>
        function testAjax(postType) {
            jQuery.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'test_medya_eduarte_posts',
                    post_type: postType
                },
                success: function(response) {
                    jQuery('#ajax-test-result').html(response);
                },
                error: function() {
                    jQuery('#ajax-test-result').html('<p style="color: red;">Erro no AJAX</p>');
                }
            });
        }
        </script>
        <?php
    }
});

// Função para debug das strings localizadas
add_action('admin_footer', function() {
    if (isset($_GET['page']) && $_GET['page'] === 'medya-eduarte-moveis') {
        ?>
        <script>
        console.log('medyaEduarte object:', typeof medyaEduarte !== 'undefined' ? medyaEduarte : 'UNDEFINED');
        
        // Testa se o AJAX está funcionando
        if (typeof medyaEduarte !== 'undefined') {
            console.log('AJAX URL:', medyaEduarte.ajaxUrl);
            console.log('Nonce:', medyaEduarte.nonce);
            console.log('Strings:', medyaEduarte.strings);
        }
        </script>
        <?php
    }
});

// Para usar via URL:
// /wp-admin/admin.php?page=medya-eduarte-moveis&test_ajax=1
